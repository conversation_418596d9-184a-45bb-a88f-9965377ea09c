﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;
using System.Data.SqlClient;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";


        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();


           
        }


        public ScriptsCustom()
        {
            Initialize();
        }

        // SGR 25102016 
        

        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool AC_FormControlOnChange_MLS_TYPE_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 8/19/2013 Custom fill subject
            // when type is E-mail Sent
            Form doForm = (Form)par_doCallingObject;

            try
            {

                // goP.TraceLine("", "", sProc)
                // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

                string sQTID = Convert.ToString(doForm.oVar.GetVar("AC_QTID"));

                if (sQTID != "")
                {
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2)) == 6 & (Convert.ToString(doForm.doRS.GetFieldVal("TXT_Subj")) == ""))
                    {
                        scriptManager.RunScript("Activity_FillSubject", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sQTID);

                    }
                }
            }
            catch (Exception ex)
            {
                goLog.Log(sProc, " failed with error " + goErr.GetLastError());
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = doForm.GetCreateLinkedSourceRecSUID;

            // TLD 8/19/2013 Custom fill subject
            // If the ID is not blank, find out if coming from an AC
            if (sID != "" & sID != null)
            {
                if (goTR.GetFileFromSUID(sID) == "QT")
                {
                    // Set var for use on change of Type for QT ID?
                    doForm.oVar.SetVar("AC_QTID", sID);
                    if (doForm.GetMode() == "CREATION" & Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2)) == 6)
                        scriptManager.RunScript("Activity_FillSubject",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections, null, sID);
                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 2/17/2014 Enforce Product when Type is Sales Visit
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2)) == 11 & doForm.doRS.IsLinkEmpty("LNK_Related_PD") == true)
            {
                doForm.MoveToTab(2);     // Notes
                doForm.MoveToField("LNK_Related_PD");
                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_RELATED_PD"), "", "", "", "", "", "", "", "", "LNK_RELATED_PD");
                return false;
            }
            // SGR 10052015 TKT:742
            doForm.doRS.SetFieldVal("LNK_RELATED_BR", doForm.doRS.GetFieldVal("LNK_RELATED_PD%%LNK_RELATED_BR", 2), 2);

            // SKO 10302015 TKT:769 For Activities, on save make sure that brand, vendor and division fills from product.
            doForm.doRS.SetFieldVal("LNK_RELATED_VE", doForm.doRS.GetFieldVal("LNK_RELATED_PD%%LNK_RELATED_VE", 2), 2);
            doForm.doRS.SetFieldVal("LNK_RELATED_DV", doForm.doRS.GetFieldVal("LNK_RELATED_PD%%LNK_RELATED_DV", 2), 2);
            // doForm.doRS.SetFieldVal("LNK_RELATED_BR", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_BR", 2), 2)

            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            // VS 09092015 TKT#706 : Update Last Date for Company
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doRS;
            return true;
        }
        public bool Activity_FillSubject_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm
            // par_doArray: Unused
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 8/19/2013 Called from AC_FormOnLoadRecord_Post
            // and AC_FormControlOnChange_MLS_Type_Post
            Form doForm = (Form)par_doCallingObject;

            try
            {
                string sQTID = par_s1;
                string sSubj = "";
                string sQTNo = "";
                string sQTRefNo = "";

                // DEBUG
                // goP.TraceLine("In " & sProc & " doForm's EML_EMAIL: " & Trim(doForm.dors.GetFieldVal("EML_EMAIL")), "", sProc)

                if (sQTID != "")
                {
                    clRowSet doQTRS = new clRowSet("QT", 3, "GID_ID='" + sQTID + "'", null, "TXT_QuoteNo, TXT_RFQNo, LNK_To_CO%%TXT_CompanyName", 1);
                    if (doQTRS.GetFirst() == 1)
                    {
                        sQTNo = Strings.Trim(Convert.ToString(doQTRS.GetFieldVal("TXT_QuoteNo", clC.SELL_FRIENDLY)));
                        sQTRefNo = Strings.Trim(Convert.ToString(doQTRS.GetFieldVal("TXT_RFQNO", clC.SELL_FRIENDLY)));
                        if (sQTNo != "")
                        {
                            if (sQTRefNo != "")
                            {
                                sSubj = "Quote #" + sQTNo + " from " + doQTRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) + " regarding " + doQTRS.GetFieldVal("LNK_To_CO%%TXT_CompanyName") + ", " + sQTRefNo;

                            }
                            else
                            {
                                sSubj = "Quote #" + sQTNo + " from " + doQTRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) + " regarding " + doQTRS.GetFieldVal("LNK_To_CO%%TXT_CompanyName");

                            }
                        }
                        else if (sQTRefNo != "")
                            sSubj = "Quote from " + doQTRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) + " regarding " + doQTRS.GetFieldVal("LNK_To_CO%%TXT_CompanyName") + ", " + sQTRefNo;
                        else
                            sSubj = "Quote from " + doQTRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) + " regarding " + doQTRS.GetFieldVal("LNK_To_CO%%TXT_CompanyName");
                        doForm.doRS.SetFieldVal("TXT_Subj", sSubj);
                    }
                }
            }
            catch (Exception ex)
            {
                goLog.Log(sProc, " failed with error " + goErr.GetLastError());
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool AutoQuoteDuplicate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 4/26/2013 Custom fields
            par_bRunNext = false;

            // MI 4/25/07 CREATED BY MI 4/22/07
            // PURPOSE:
            // Duplicate an existing Quote and its line items allowing the user to connect a different
            // Contact, Company, etc.

            string sID;
            clRowSet doRowset;
            string sFileName;
            Form doF;
            string sOrigQuoteName;
            string sOrigQuoteID;

            // Check selected record
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            // goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
            if (sFileName != "QT")
            {
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return true;
            }

            // Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                goUI.NewWorkareaMessage("You cannot duplicate the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return true;
            }

            // Copy the selected record
            // Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*,LNK_CONNECTED_QL%%SYS_Name", 1);
            if (doRowset.Count() < 1)
            {
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return true;
            }
            else
            {
                sOrigQuoteName = Convert.ToString(doRowset.GetFieldVal("SYS_Name"));
                sOrigQuoteID = Convert.ToString(doRowset.GetFieldVal("GID_ID"));
            }

            // Create the new Quote form
            doF = new Form(sFileName, "", "CRU_" + sFileName);
            doF.oVar.SetVar("QuoteOpeningMode", "Duplicate");
            doF.oVar.SetVar("QuoteOrinalQuoteID", sID);
            doF.SetControlVal("NDB_MMO_Lines", Convert.ToString(doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name")));
            doF.doRS.SetFieldVal("TXT_Description", doRowset.GetFieldVal("TXT_Description", 2), 2);
            doF.doRS.SetFieldVal("CUR_Subtotal", doRowset.GetFieldVal("CUR_Subtotal", 2), 2);
            doF.doRS.SetFieldVal("CUR_Total", doRowset.GetFieldVal("CUR_Total", 2), 2);
            // Set History tab & Cloned from Quote
            doF.doRS.SetFieldVal("MMO_History", "Duplicated from Quote" + sOrigQuoteName);
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sOrigQuoteID);

            // TLD 4/26/2013 Custom fields
            doF.doRS.SetFieldVal("MLS_Budgetary", doRowset.GetFieldVal("MLS_Budgetary", 2), 2);

            doF.MessagePanel("This is a duplicate of the Quote '" + sOrigQuoteName + "'." + Constants.vbCrLf + "Fill out the form and click Save or click Modify Lines to add, edit or remove them.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Info.gif");

            // 'Copy to the new rowset
            // If Not goData.CopyRecord(doRowset, doF.doRS) Then
            // goErr.SetError(35000, sProc, "Copying the selected Quote '" & sID & "' failed.")
            // Return False
            // End If
            // doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            // doF.doRS.ClearLinkAll("LNK_OriginatedBy_CN")
            // doF.doRS.ClearLinkAll("LNK_To_CO")

            // 'Save the new record (?)
            // If doNewRowset.Commit() = 0 Then
            // goErr.SetWarning(30200, sProc, "", "An error occurred duplicating the selected Quote.", "", "", "", "", "", "", "", "", "")
            // doRowset = Nothing
            // doNewRowset = Nothing
            // Return False
            // End If

            goUI.Queue("FORM", doF);

            // Clean up objects
            doRowset = null/* TODO Change to default(_) if this is not a reference type */;

            return true;
        }
        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "0", string par_s4 = "0", string par_s5 = "0" )
        {
            // par_doCallingObject: Optional: editable clRowset of the Quote header record. If passed, 
            // this rowset is updated and the record on disk is not touched. If not passed,
            // the record on disk is updated. in this case par_s1 (Quote ID) is mandatory.
            // par_doArray: Unused.
            // par_s1: GID_ID of the Quote to calculate. Mandatory even when par_doCallingObject is passed.
            // par_s2: Optional: ID of the Quote Line for which to take amounts from par_s3 and par_s4
            // instead of from disk. This is to allow recalculating the quote from a Quote Line
            // which hasn't been saved yet. If "", all quote lines are read from disk and par_s3 and par_s4
            // are ignored.
            // par_s3: Mandatory only if par_s2 <> "". CHK_TAXABLE value (1 or 0) from Quote Line (ID passed via par_s2). 
            // Use goTr.CheckboxToString(doRS.GetFieldVal("CHK_TAXABLE", 2)) to get it in this format.
            // par_s4: mandatory only is par_s2 <> "". CUR_SUBTOTAL as unformatted number from Quote Line (ID passed via par_s2).
            // Use goTr.CurrToString(doRS.GetFieldVal("CUR_SUBTOTAL", 2)) to get it in this format.
            // par_s5: Unused.

            // Old NGP parameters:
            // par_s1: ID of the Quote. If blank, all parameters are read from global variables named QUOTE_<FieldName>
            // and the Quote is saved on disk.
            // par_s2: System-format value of Quote's CHK_INCLUDETAXCHARGES as boolean: 1 (default) or 0.
            // par_s3: System-format value of Quote's SR__SALESTAXPERCENT as single real. Default is '0'.
            // par_s4: System-format value of Quote's CUR_OTHERCHARGE as currency. Default is '0'.
            // par_s5: System-format value of Quote's CUR_SHIPPING as currency. Default is '0'.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/1/2013 Customizations
            // MI 2/23/07 Mods
            // PURPOSE:
            // Calculate totals of a Quote from Quote Lines independent of the form context.
            // RETURNS:
            // True if successful, False if not with SetError. Returns calculation results
            // via gop:SetVar() in the following variables:
            // CUR_SUBTOTAL
            // CUR_SUBTOTALT
            // CUR_SALESTAX
            // CUR_TOTAL	

            //string doLines;
            clRowSet doLines = default(clRowSet);
            int lI;
            decimal cWork=0;        // Non-taxable subtotals
            decimal cWorkT=0;       // Taxable subtotals only
            double rTaxPercent;
            decimal cTaxAmount;
            decimal cOtherCharge;
            decimal cTotalAmount;
            decimal cShipAmount;
            // Dim s1 As String = par_s1   'Quote GID_ID
            // Dim s2 As String = par_s2   'Quote Line GID_ID 
            // Dim s3 As String = par_s3
            // Dim s4 As String = par_s4
            // Dim s5 As String = par_s5
            clRowSet doRS = (clRowSet)par_doCallingObject;      // Quote rowset
            bool bCommit = false;
            bool bQLTaxable=true;
            decimal cQLSubtotal=0;
            bool bQLFound = false;
            string sFiles = ""; // FIL_INCLUSIONS from Quote Line Models
            bool bUpdInclusions = false;
            // TLD 9/24/2014 Added
            double rProb = 0;
            // TLD 9/25/2014 Added
            decimal cWon = 0; // Total of QLs where status is Won
            int iQTStatus = 0;

            // Vars are:
            // s1 = QT GID_ID
            // s2 = QL GID_ID
            // s3 = QL CHK_TAXABLE
            // s4 = QL CUR_Subtotal

            // Vars used to be:
            // s1 = goP.GetVar("QUOTE_GID_ID")
            // s2 = goP.GetVar("QUOTE_CHK_INCLUDETAXCHARGES")
            // s3 = goP.GetVar("QUOTE_SR__SALESTAXPERCENT")
            // s4 = goP.GetVar("QUOTE_CUR_OTHERCHARGE")
            // s5 = goP.GetVar("QUOTE_CUR_SHIPPING")
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



            // ------------- Validate parameters ----------------
            if (!goData.IsFileValid(goTR.GetFileFromSUID(par_s1.ToString())))
            {
                goErr.SetError(10100, sProc, null/* Conversion error: Set to default value for this argument */, goTR.GetFileFromSUID(par_s1), "File extracted from SUID in par_s1: '" + par_s1 + "'. Be sure to pass the GID_ID value of the Quote to recalculate.");
                // 10100: Invalid file name '[1]'. [2]
                return false;
            }
            if (par_s2.ToString() == "")
            {
                // Override QL ID not provided - ignore the rest of QL parameters
                par_s3 = "";
                par_s4 = "";
            }
            else
            {
                // Quote Line GID_ID was passed
                // QL's CHK_Taxable value
                if (par_s3.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s3 is blank. QL's CHK_Taxable value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    bQLTaxable = goTR.StringToCheckbox(par_s3,false,ref par_iValid);
                // QL's CUR_Subtotal value
                if (par_s4.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s4 is blank. QL's CUR_Subtotal value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    cQLSubtotal = Convert.ToDecimal(par_s4);
            }


            // -------------- Read Lines and calculate their amounts ------------
            // TLD 9/25/2014 Add status to RS
            // CS 12/2/08: Check if MO.FIL_INCLUSIONS exists. If so need to get it in the QL RS below
            if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
            {
                doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS, MLS_Status");

            }
            else
            {
                doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, MLS_Status");

            }
            // Browse through the rowset
            lI = 1;
            if (doLines.GetFirst() == 1)
            {
                do
                {
                    // Add up Quote Lines. Skip the one for which GID_ID is passed via par_s2
                    if (par_s2.ToString() == "" | Strings.UCase(par_s2) != Strings.UCase(doLines.GetFieldVal("GID_ID", 1).ToString()))
                    {
                        if (Convert.ToInt32(doLines.GetFieldVal("CHK_TAXABLE", 2)) == 1)
                        {
                            cWorkT += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));

                        }
                        else
                            cWork += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
                        // CS 12/2/08: Get value from QL%%MO FIL_INCLUSIONS field
                        if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                        {
                            // Check if the file has already been added
                            if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
                            {
                                // If this is the first file don't add a vbcrlf in front of it
                                if (sFiles == "")
                                    sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));
                                else
                                    sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
                            }
                        }
                        // TLD 9/25/2014 Check QL status
                        if (Convert.ToInt32(doLines.GetFieldVal("MLS_Status", 2)) == 2)
                            cWon += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
                    }

                    if (doLines.GetNext() == 0)
                        break;
                    lI += 1;
                }
                while (true);
            }
            // delete(doLines)
            doLines = null;

            // Add the Quote Line passed via parameters
            if (par_s2 != "")
            {
                // CS 12/31/08: Get Fil_Inclusions of QL passed via parameter
                // This code needs to be run if you open a QL directly from
                // a QL view and it has file inclusions
                // CS 1/8/09: Check if FIL_INCLUSIONS exist in db first.
                if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                {
                    doLines = new clRowSet("QL", 3, "GID_ID='" + par_s2.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS");
                    if (doLines.GetFirst() == 1)
                    {
                        // If goData.IsFieldValid("MO", "FIL_INCLUSIONS") Then
                        // Check if the file has already been added
                        if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
                        {
                            // If this is the first file don't add a vbcrlf in front of it
                            if (sFiles == "")
                                sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));
                            else
                                sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
                        }
                    }
                }

                if (bQLTaxable == true)
                {
                    cWorkT += cQLSubtotal;

                }
                else
                {
                    cWork += cQLSubtotal;

                }
            }

            // Subtotal = cWork + cWorkT
            // SubtotalT = cWorkT

            // ---------- Pull up the Quote -----------
            if (doRS == null)
            {
                // Get the quote from disk
                bCommit = true;
                // doRS = New clRowSet("QT", 1, _
                // "GID_ID='" & par_s1 & "'", _
                // "DTT_TIME ASC", _
                // "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL")
                // CS 7/26/07: Currently if you open a QT that has a required field missing
                // such as NA date, edit a QL and save the QL you get an error. Trying to avoid
                // that by bypassing validation.
                doRS = new clRowSet("QT", 1, "GID_ID='" + par_s1 + "'", "DTT_TIME ASC", "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL", 0, null, null, null, null, null, true);
            }
            else
                // Validate the passed Rowset object
                if (Strings.UCase(doRS.GetFileName()) != "QT")
            {
                goErr.SetError(35000, sProc, "The file of the rowset in par_doCallingObject parameter is not QT (Quote). Either pass a Quote rowset or only pass the Quote GID_ID in par_s1 parameter.");
                return false;
            }

            // ----------- Read Quote data and calculate -------------
            if (doRS.GetFirst() ==1)
            {
                // CS 12/2/08: Get the value of the 'Do not update inclusions' on QT
                // If checked, do not update FIL_INCLUSIONS field on QT
                if (goData.IsFieldValid("QT", "CHK_NOUPDINCLUSIONS"))
                {
                    if (Convert.ToInt32(doRS.GetFieldVal("CHK_NOUPDINCLUSIONS", 2)) == 0)
                    {
                        bUpdInclusions = true;

                    }
                }
                rTaxPercent = Convert.ToDouble(doRS.GetFieldVal("SR__SALESTAXPERCENT", clC.SELL_SYSTEM));    // s3
                cTaxAmount = Convert.ToDecimal(cWorkT) * Convert.ToDecimal(rTaxPercent) / 100;     // cTaxAmount goes into CUR_SALESTAX
                                                             // If the 'Include Tax/Charges' check-box is not checked, do not add tax,
                                                             // other charge and shipping to Total. 
                if (Convert.ToInt32(doRS.GetFieldVal("CHK_INCLUDETAXCHARGES", clC.SELL_SYSTEM)) == 1)
                {
                    cOtherCharge = Convert.ToDecimal(doRS.GetFieldVal("CUR_OTHERCHARGE", clC.SELL_SYSTEM));   // s4
                    cShipAmount = Convert.ToDecimal(doRS.GetFieldVal("CUR_SHIPPING", clC.SELL_SYSTEM));   // s5
                                                                                       // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT + cTaxAmount + cOtherCharge + cShipAmount;
                }
                else
                    // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT;
                // TLD 9/25/2014 for Quote Status
                iQTStatus = Convert.ToInt32(doRS.GetFieldVal("MLS_Status", 2));
            }
            else
            {
                // goP.TraceLine("doRS GetFirst not found", "", sProc)
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                goErr.SetError(30032, sProc, "", "Quote");
                // The linked [1] can't be updated because it can't be found. 
                // goP.TraceLine("Return False", "", sProc)
                return false;
            }

            // --------------- Update calculated fields ----------------
            // TLD 9/24/2014 Need to set SR__probability to 100% when Status is Won
            // best place to do it as far as I can tell
            if (Convert.ToInt32(doRS.GetFieldVal("MLS_Status", 2)) == 2)
            {
                rProb = 100;
                doRS.SetFieldVal("SR__Probability", rProb, 2);
            }
            else
                rProb = Convert.ToDouble(doRS.GetFieldVal("SR__Probability", 2));
            doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cWork + cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SUBTOTALT", goTR.RoundCurr(cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SALESTAX", goTR.RoundCurr(cTaxAmount), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_TOTAL", goTR.RoundCurr(cTotalAmount), clC.SELL_SYSTEM);
            // TLD 9/24/2014 Use var
            // TLD 5/1/2013 Custom calcs
            // doRS.SetFieldVal("CUR_ValueIndex", (goTR.RoundCurr(cWork + cWorkT) * doRS.GetFieldVal("SR__Probability", 2)) / 100, 2)
            doRS.SetFieldVal("CUR_ValueIndex", goTR.RoundCurr(Convert.ToDecimal(cWork + cWorkT)) * Convert.ToDecimal(rProb) / (decimal)100, 2);
            // TLD 9/25/2014 If QT status is Open or On Hold AND QL status is Won
            // Set Partial to Yes
            if (iQTStatus == 0 | iQTStatus == 1)
            {
                if (cWon != 0)
                    doRS.SetFieldVal("MLS_Partial", 1, 2);
            }
            doRS.SetFieldVal("CUR_WonValue", cWon, 2);

            // --------------Update FIL_Inclusions
            if (bUpdInclusions == true)
                doRS.SetFieldVal("FIL_INCLUSIONS", sFiles);

            // --------------- Save the Quote ---------------
            if (bCommit)
            {
                goP.SetVar("bDoNotUpdateQuoteLines", "1");
                // CS 11/5/09: Setting a variable here to let me know NOT to try to update the Qt total again in QT_RecOnSave. Issue was that if
                // you open a QT, add a Quote Line and then cancel out of the QT, the QT total did not reflect the actual total. This
                // was because CalcQuotetotal was being called here and then again in QT_RecordOnSave. We do NOT want it to be called in QT
                // RecOnSave if it was called here.
                goP.SetVar("bDoNotRecalcQuoteTotal", "1");
                // Save to disk
                if (doRS.Commit() != 1)
                {
                    // goP.TraceLine("Commit failed, raising error", "", sProc)
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    // CS 11/6/09: Set in Ql_RecordOnSave
                    goP.DeleteVar("bDoNotRecalcQuoteTotal");
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                    goErr.SetError(35000, sProc, "Error updating the Quote '" + par_s1 + "'."); // CS
                                                                                                // goP.TraceLine("Return False", "", sProc)
                    return false;
                }
                else
                {
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    goP.DeleteVar("bDoNotRecalcQuoteTotal"); // CS 11/6/09
                    if (!(doRS == null))
                        doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)
            par_doCallingObject = doLines;
            return true;
        }

        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/29/2013 Disabed Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/29/2013 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/29/2013 Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                        {
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CN", "MergeFail");

                        }
                        else
                        {
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CN", "Merge");

                        }
                    }
                }
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/29/2013 Disabed Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/29/2013 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/29/2013 Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CO", "MergeFail");
                        else
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CO", "Merge");
                    }
                }
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS;
            doRS = (clRowSet)par_doCallingObject;

            string sCurVol = "";
            string sPotVol = "";
            int iCurCount = Convert.ToInt32(doRS.GetLinkCount("LNK_Current_PD"));
            int iPotCount = Convert.ToInt32(doRS.GetLinkCount("LNK_Potential_PD"));

            // SKO 9/07/2015 TKT:706 Target Account Matrix Profiling
            // Copy LNK_Current_PD to LNK_Potential_PD
            doRS.SetFieldVal("LNK_Potential_PD", doRS.GetFieldVal("LNK_Current_PD"));

            // Record total selections from LNK_Current_PD and LNK_Potential_PD
            doRS.SetFieldVal("INT_CURCOUNT", iCurCount, 2);
            doRS.SetFieldVal("INT_POTCOUNT", iPotCount, 2);

            // Calculate Potential Percentage & Potential Portfolio
            clRowSet doPDRS = new clRowSet("PD", 3, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "GID_ID");
            int iPDCount = Convert.ToInt32(doPDRS.Count());
            if (iPDCount != 0)
            {
                if (iPotCount != 0)
                    doRS.SetFieldVal("SR__POTENTIALPERC", (iCurCount / (double)iPotCount) * 100, 2);
                doRS.SetFieldVal("SR__POTENTIALPORTFOLIO", (iPotCount / (double)iPDCount) * 100, 2);

                doRS.SetFieldVal("SR__ProdLinePot", (iPotCount - iCurCount), 2);
                sCurVol = Strings.Left(doRS.GetFieldVal("MLS_CURVOLUME").ToString(), 1);
                sPotVol = Strings.Left(doRS.GetFieldVal("MLS_POTVOLUME").ToString(), 1);
                if (sCurVol == "<")
                {
                    sCurVol = "Z";

                }
                if (sPotVol == "<")
                {
                    sPotVol = "Z";

                }

                // set field to cur & pot
                doRS.SetFieldVal("TXT_CURANDPOT", sCurVol + sPotVol);
            }

            // ---------- Target Account -----------------
            // Set Product Potential Quadrant
            double rTotalPortfolio = Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2));
            double rPotentialProduct = Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPERC", 2));

            if (rTotalPortfolio >= 51 & rTotalPortfolio <= 100)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                
                {
                    // Set to 1
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "1");

                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    // Set to 3
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "3");
                }
                   
            }

            if (rTotalPortfolio >= 0 & rTotalPortfolio <= 50)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    // Set to 2
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "2");
                }
                    

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    // Set to 4
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "4");
                }
                    
            }

            // Because COs are updated nightly to set custom
            // date fields, need to write to custom mod time and mod by fields
            // AutoCOUpdate does NOT run recordonsave
            doRS.SetFieldVal("TXT_CusModBy", goP.GetMe("CODE"));
            doRS.SetFieldVal("DTT_CusModTime", "Today|Now");
            // ---------TLD 7/24/2012 End Target Account Matrix Profiling

            par_doCallingObject = doRS;
            return true;
        }
        //public bool GenerateSysName_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    // MI 10/23/07 Added 'UTC' to AT Name.
        //    // par_doCallingObject: Form object calling this script. Do not delete in script!
        //    // par_doArray: Unused.
        //    // par_s1: Unused.
        //    // par_s2 to par_s5: Unused.
        //    // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    // TLD 4/24/2013 Changed to _Pre
        //    // TLD 2/27/2013 Added

        //    clRowSet doRS = (clRowSet)par_doCallingObject;
        //    string sTemp = "";
        //    string sTemp2 = "";
        //    string sTemp3 = "";
        //    string sFileName = doRS.GetFileName();
        //    string sResult = "";
        //    // Dim doLink As clRowSet

        //    switch (Strings.UCase(sFileName))
        //    {
        //        case "MO"        // MODEL		TXT_ModelName
        //       :
        //            {
        //                if (!doRS.IsLoaded("TXT_ModelName"))
        //                    goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_ModelName");
        //                // TLD 5/21/2013
        //                // 'MI 4/2/08 TXT_Description replaces MMO_Description as of 4/3/08. MMO_Description remains in existing DBs.
        //                // If goData.IsFieldValid("MO", "TXT_Description") Then
        //                // If Not doRS.IsLoaded("TXT_Description") Then
        //                // goErr.SetError(35103, sProc, , sFileName & ".TXT_Description")
        //                // '35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //                // End If
        //                // Else
        //                // If Not doRS.IsLoaded("MMO_Description") Then
        //                // goErr.SetError(35103, sProc, , sFileName & ".MMO_Description")
        //                // '35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //                // End If
        //                // End If

        //                sTemp = Convert.ToString(doRS.GetFieldVal("TXT_ModelName", 0, 80));
        //                // MI 4/2/08 TXT_Description replaces MMO_Description as of 4/3/08. MMO_ field remains in existing DBs.
        //                // If goData.IsFieldValid("MO", "TXT_Description") Then
        //                // sTemp2 = doRS.GetFieldVal("TXT_ProductCatalogName", , 44)
        //                // Else
        //                // sTemp2 = doRS.GetFieldVal("MMO_Description", , 44)
        //                // End If
        //                if (sTemp == "")
        //                    sTemp = "?";
        //                sResult = sTemp;

        //                // Model Name (30 + 2)
        //                // Description (remaining space + 1)
        //                // If sTemp2 = "" Then
        //                // sResult = sTemp
        //                // Else
        //                // sResult = sTemp & " " & sTemp2
        //                // End If

        //                par_bRunNext = false;
        //                break;
        //            }

        //    }

        //    if (!par_bRunNext)
        //    {
        //        sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
        //        sResult = goTR.Replace(sResult, Constants.vbTab, " ");
        //        par_oReturn = sResult;
        //    }
        //    par_doCallingObject = doRS;
        //    return true;
        //}
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }

        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/29/2013 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'", null, "**", 0, null, null, null, null, null, true, true, true, true, 0, null, true);
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Microsoft.VisualBasic.Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));

                                    }
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (doRSMergeTo.GetFieldVal(sField) == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));

                                    }
                                    else
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));

                                    }
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);

                                    }
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);

                                    }
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | sLinkType[1] == "2")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (Convert.ToString(doRSMergeTo.GetFieldVal(aLinks.GetItem(i))) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);
                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;
            par_doCallingObject = doRSMerge;
            return true;
        }

        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJournal = "";
            string sWork = "";

            switch (Strings.UCase(par_s5))
            {
                case "MERGE":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    scriptManager.RunScript("MergeRecord",ref par_doCallingObject,ref par_oReturn, ref par_bRunNext,ref par_sSections);
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SGR 10052015 TKT:742
            Form doForm = (Form)par_doCallingObject;
            doForm.doRS.SetFieldVal("LNK_RELATED_BR", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_BR", 2), 2);


            // SKO 10302015 TKT:769 For opps , on save make sure that brand, vendor and division fills from product
            doForm.doRS.SetFieldVal("LNK_RELATED_VE", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_VE", 2), 2);
            doForm.doRS.SetFieldVal("LNK_RELATED_DV", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_DV", 2), 2);
            // doForm.doRS.SetFieldVal("LNK_RELATED_BR", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_BR", 2), 2)

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            // VS 09092015 TKT#706 : Update Last Date for Company
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doRS;
            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SGR 10052015 TKT:742
            Form doForm = (Form)par_doCallingObject;
            doForm.doRS.SetFieldVal("LNK_RELATEDBRAND_BR", doForm.doRS.GetFieldVal("LNK_RELATED_PD%%LNK_RELATED_BR", 2), 2);
            // 
            // Dim doLinkPD As New clArray
            // doLinkPD = doForm.doRS.GetLinkVal("LNK_RELATED_PD%%LNK_RELATED_BR", doLinkPD)
            // If doLinkPD.GetDimension > 0 Then
            // doForm.doRS.SetFieldVal("LNK_RELATED_BR", doForm.doRS.GetFieldVal("LNK_RELATED_PD%%LNK_RELATED_BR", 2), 2)
            // ' doForm.doRS.GetFieldVal("LNK_RELATED_BR", 2)
            // End If
            par_doCallingObject = doForm;
            return true;
        }
        public bool PR_CalcValue_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // VS 10052015 TKT#741 : Recalculate Project value from Connected Opps

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/9/2011 calculate Project Value
            // from connected OPs
            decimal cValueFld = 0;
            clArray doLink;

            doLink = new clArray();
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Connected_OP", ref doLink, true, 0, -1, "A_a", ref oTable);

            for (int i = 1; i <= doLink.GetDimension(); i++)
            {
                clRowSet doOPRS = new clRowSet("OP", 3, "GID_ID='" + doLink.GetItem(i) + "'", null/* Conversion error: Set to default value for this argument */, "CUR_VALUE", 1);
                if (doOPRS.GetFirst() ==1)
                {
                    cValueFld = Convert.ToDecimal(cValueFld) + Convert.ToDecimal(doOPRS.GetFieldVal("CUR_VALUE"));
                    // cMarginFld = cMarginFld + doOPRS.GetFieldVal("CUR_MARGIN")

                    doForm.doRS.SetFieldVal("CUR_Value", cValueFld);
                }
            }

            // TLD 5/16/2011 Added to calc CUR_OppValue
            doForm.doRS.SetFieldVal("CUR_OppValue", Convert.ToDecimal(doForm.doRS.GetFieldVal("SR__QUANTITY")) * Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UnitPrice")));

            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormControlOnChange_BTN_CREATEOPP_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // VS 10052015 TKT#741 : Recalculate Project value from Connected Opps

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/9/2011 Creates OP behind the scenes
            // when user clicks button

            // Saves PR if new -- leaves open, keeps RS available
            if (doForm.GetMode() == "CREATION")
            {
                if (doForm.Save(3, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                    return false;
            }

            // Before creating the new OP, need to enforce fields on the PR
            // that are copied to the OP
            if (doForm.doRS.IsLinkEmpty("LNK_For_CO") == true)
            {
                doForm.MoveToField("LNK_For_CO");
                goErr.SetWarning(30029, sProc, "You must select a Company before creating an Opportunity.", doForm.GetFieldLabel("LNK_For_CO"), "", "", "", "", "", "", "", "", "LNK_For_CO");
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_OPPORTUNITYPRODUCT_PD") == true)
            {
                doForm.MoveToTab(13);     // Opps
                doForm.MoveToField("LNK_OPPORTUNITYPRODUCT_PD");
                goErr.SetWarning(30029, sProc, "You must select an Opp Product before creating an Opportunity.", doForm.GetFieldLabel("LNK_OPPORTUNITYPRODUCT_PD"), "", "", "", "", "", "", "", "", "LNK_OPPORTUNITYPRODUCT_PD");
                return false;
            }

            if (Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1)) == "")
            {
                // SGR 10062015 Commented below line of code
                // doForm.MoveToTab(1)     'Details
                doForm.MoveToField("DTE_EXPCLOSEDATE");
                goErr.SetWarning(30029, sProc, "You must select an Exp Close Date before creating an Opportunity.", doForm.GetFieldLabel("DTE_EXPCLOSEDATE"), "", "", "", "", "", "", "", "", "DTE_EXPCLOSEDATE");
                return false;
            }

            if (Convert.ToString(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1)) == "")
            {
                // doForm.MoveToTab(2)     'Journal
                doForm.MoveToField("DTE_NEXTACTIONDATE");
                goErr.SetWarning(30029, sProc, "You must select a Next Action Date before creating an Opportunity.", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                return false;
            }

            // 'TLD 5/25/2011 Removed mandatory requirement
            // 'TXT_Description on OP not mandatory.
            // If doForm.doRS.GetFieldVal("MMO_Description") = "" Then
            // doForm.MoveToField("MMO_Description")
            // goErr.SetWarning(30029, sProc, "You must enter a Description before creating an Opportunity.", doForm.GetFieldLabel("MMO_Description"), "", "", "", "", "", "", "", "", "MMO_Description")
            // Return False
            // End If

            // Create a rowset and create a new record.
            clRowSet doOPRS = new clRowSet("OP", 2, null, null, null, 0, null, "CRL_OP", null, null, null, true);

            // Set new OP field values
            doOPRS.SetFieldVal("CUR_UNITVALUE", doForm.doRS.GetFieldVal("CUR_UNITPRICE"));
            doOPRS.SetFieldVal("SR__QTY", doForm.doRS.GetFieldVal("SR__QUANTITY"));
            doOPRS.SetFieldVal("SI__Probability", doForm.doRS.GetFieldVal("SI__Probability"));
            doOPRS.SetFieldVal("TXT_DESCRIPTION", doForm.doRS.GetFieldVal("MMO_DESCRIPTION", 0, 50));
            doOPRS.SetFieldVal("LNK_Creditedto_US", goP.GetMe("ID"));
            doOPRS.SetFieldVal("LNK_FOR_CO", doForm.doRS.GetFieldVal("LNK_FOR_CO"));
            doOPRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            doOPRS.SetFieldVal("DTE_NEXTACTIONDATE", doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE"));
            doOPRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("GID_ID"));
            doOPRS.SetFieldVal("LNK_CreditedTo_US", doForm.doRS.GetFieldVal("LNK_OriginatedBy_US", 2), 2);
            doOPRS.SetFieldVal("LNK_OriginatedBy_CN", doForm.doRS.GetFieldVal("LNK_OriginatedBy_CN", 2), 2);

            // Set OP Temp Product to PR Product (this is cleared)
            // doOPRS.SetFieldVal("LNK_HAS_PD", doForm.doRS.GetFieldVal("LNK_OPPORTUNITYPRODUCT_PD"))
            // Set OP Product to OP temp product field
            doOPRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_OPPORTUNITYPRODUCT_PD"));
            // Clear OP temp product field
            // doOPRS.ClearLinkAll("LNK_HAS_PD")

            // Reset fields on Project
            doForm.doRS.SetFieldVal("LNK_RELATED_PD", doForm.doRS.GetFieldVal("LNK_OPPORTUNITYPRODUCT_PD"));
            doForm.doRS.SetFieldVal("LNK_CONNECTED_OP", doOPRS.GetFieldVal("GID_ID"));
            doForm.doRS.ClearLinkAll("LNK_OPPORTUNITYPRODUCT_PD");
            doForm.doRS.SetFieldVal("SR__QUANTITY", 0);
            doForm.doRS.SetFieldVal("CUR_UNITPRICE", 0);
            doForm.doRS.SetFieldVal("SI__Probability", 0);
            // TLD 5/16/2011 Added to reset Opp Value
            doForm.doRS.SetFieldVal("CUR_OppValue", 0);

            // doForm.doRS.UpdateLinkState("LNK_CONNECTED_OP")
            // doForm.doRS.UpdateLinkState("LNK_HAS_PD")

            // Save OP RS
            if (doOPRS.Commit() == 0)
                return false;

            // Saves form again, leave open
            if (doForm.Save(3, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                doForm.doRS.UpdateLinkState("LNK_CONNECTED_OP");
                return false;
            }

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OP");

            doOPRS = null/* TODO Change to default(_) if this is not a reference type */;

            // Recalc PR?
            scriptManager.RunScript("PR_CalcValue", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            // goUI.RefreshOpenDesktop()
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormControlOnChange_BTN_PRRECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // VS 10052015 TKT#741 : Recalculate Project value from Connected Opps

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/9/2011 Calls script to calculate Project Value
            // from connected OPs
            scriptManager.RunScript("PR_CalcValue", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_FilterModel_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/6/2013 Filter LNK_For_MO to
            // LNK_Related_VE%%LNK_Connected_MO
            Form doForm = (Form)par_doCallingObject;
            string sFilterIni = "";
            string sCondition = "";
            string sVendorID = "";
            int cCount = 0;

            if (doForm.doRS.IsLinkEmpty("LNK_Related_VE") == false)
            {
                cCount = 1;
                sVendorID = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_VE", 0, 0, true, 1));
                goTR.StrWrite(ref sFilterIni, "CONDITION", "LNK_Related_VE=" + sVendorID);
                goTR.StrWrite(ref sFilterIni, "C1CONDITION", "0");
                goTR.StrWrite(ref sFilterIni, "C1FIELDNAME", "<%LNK_RELATED_VE%>");
                goTR.StrWrite(ref sFilterIni, "C1VALUE1", sVendorID);
                goTR.StrWrite(ref sFilterIni, "ACTIVE", "1");
                goTR.StrWrite(ref sFilterIni, "CCOUNT", "1");
                goTR.StrWrite(ref sFilterIni, "SORT", "SYS_NAME ASC");
                goTR.StrWrite(ref sFilterIni, "SORT1", "SYS_NAME");
                goTR.StrWrite(ref sFilterIni, "FILE", "MO");
                goTR.StrWrite(ref sFilterIni, "TABLENAME", "MO");
                goTR.StrWrite(ref sFilterIni, "DIRECTION1", "1");
            }

            doForm.SetFilterINI("LNK_For_MO", sFilterIni);
            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_FormControlOnChange_BTN_SAVECRU_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            // IF gbWriteLog THEN oLog is clLogObj(sProc, "Start", 3)

            // TLD 4/26/2013 Copy custom fields
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;

            if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                return false;

            Form doNewForm;
            string sID;
            string sToCo;
            string SInQT;
            string SRelPR;
            string SStat;
            string sReason;
            string SCredUs;
            string SPeerUs;
            string SRelOp;
            string SRelTer;
            // TLD 4/26/2013 Custom fields
            double rMultiplier = 0;
            double rCustomPerc = 0;
            double rMarkup = 0;
            int iType = 0;
            int iBudgetary = 0;

            sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
            sToCo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_TO_CO"));
            SInQT = Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT"));
            SRelPR = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_PR"));
            SStat = Convert.ToString(doForm.doRS.GetFieldVal("MLS_STATUS"));
            sReason = Convert.ToString(doForm.doRS.GetFieldVal("MLS_REASONWONLOST"));
            SCredUs = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            SPeerUs = Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US"));
            SRelOp = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_OP"));
            SRelTer = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_TE"));
            // TLD 4/26/2013 custom fields
            rMultiplier = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__MultiplierUsed", 2));
            rCustomPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__CommissionPerc", 2));
            rMarkup = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__MarkupPerc", 2));
            // TLD 5/22/2013 custom fields
            iType = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2));
            iBudgetary = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Budgetary", 2));

            // Create new QL
            doNewForm = new Form("QL", "", "CRU_QL");

            // Set values in new form
            doNewForm.doRS.SetFieldVal("LNK_TO_CO", sToCo);
            doNewForm.doRS.SetFieldVal("LNK_IN_QT", SInQT);
            doNewForm.doRS.SetFieldVal("LNK_Related_PR", SRelPR);
            doNewForm.doRS.SetFieldVal("MLS_STATUS", SStat);
            doNewForm.doRS.SetFieldVal("MLS_REASONWONLOST", sReason);
            doNewForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", SCredUs);
            doNewForm.doRS.SetFieldVal("LNK_PEER_US", SPeerUs);
            doNewForm.doRS.SetFieldVal("LNK_RELATED_OP", SRelOp);
            doNewForm.doRS.SetFieldVal("LNK_RELATED_TE", SRelTer);
            // TLD 4/26/2013 Custom fields
            doNewForm.doRS.SetFieldVal("SR__MultiplierUsed", rMultiplier);
            doNewForm.doRS.SetFieldVal("SR__CommissionPerc", rCustomPerc);
            doNewForm.doRS.SetFieldVal("SR__MarkupPerc", rMarkup);
            // TLD 5/22/2013 Copye Type & Budgetary
            doNewForm.doRS.SetFieldVal("MLS_Type", iType, 2);
            doNewForm.doRS.SetFieldVal("MLS_Budgetary", iBudgetary, 2);

            doNewForm.OpenForm();
            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_FormControlOnChange_LNK_Related_VE_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/6/2013 Call script to filter LNK_For_MO
            // based on LNK_Related_VE%%LNK_Connected_MO
            scriptManager.RunScript("QL_FilterModel", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = goUI.GetLastSelected("SELECTEDRECORDID");
            string sFile = goUI.GetLastSelected("SELECTEDRECORDFILE");

            // TLD 4/26/2013 Fill custom from QT
            if (doForm.GetMode() == "CREATION")
            {
                if (!(sID == "" )& !(sID == null))
                {
                    // TLD 5/16/2013 More fields to RS
                    clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + sID + "'", null, "MLS_Budgetary, LNK_TakenAt_LO, LNK_OriginatedBy_CN, MLS_Type", 1);
                    if (doRSQT.GetFirst() ==1)
                    {
                        doForm.doRS.SetFieldVal("MLS_Budgetary", doRSQT.GetFieldVal("MLS_Budgetary", 2), 2);
                        // TLD 5/16/2013 More fields
                        doForm.doRS.SetFieldVal("LNK_TakenAt_LO", doRSQT.GetFieldVal("LNK_TakenAt_LO", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_OriginatedBy_CN", doRSQT.GetFieldVal("LNK_OriginatedBy_CN", 2), 2);
                        doForm.doRS.SetFieldVal("MLS_Type", doRSQT.GetFieldVal("MLS_Type", 2), 2);
                        doRSQT = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                }
            }

            // TLD 5/6/2013 Don't lock Vendor
            doForm.SetControlState("lnk_related_ve", 0);

            // TLD 5/6/2013 Call script to filter LNK_For_MO
            // based on LNK_Related_VE%%LNK_Connected_MO
            scriptManager.RunScript("QL_FilterModel", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            // TLD 6/4/2013 -- disable from main Gray out CUR_Cost field on the QL. 
            // The Cost will be entered as Total cost by user
            doForm.SetControlState("CUR_COST", 0);
            // TLD 6/7/2013 disable calc SR__MarkupPerc
            doForm.SetControlState("SR__MarkupPerc", 4);

            // VS 11202015 TKT#808 : if model is CHK_WIP then Do not fill LNK_FOR_PD, LNK_RELATED_VE, LNK_RELATED_BR Details. PD needs to be entered by user manually
            // Make active overwriting QL_FormonLoadRecord
            doForm.SetControlState("lnk_for_pd", 0); // Active
            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // SGR 10052015 TKT:742
            Form doForm = (Form)par_doCallingObject;
            // doForm.doRS.SetFieldVal("LNK_RELATED_BR", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_BR", 2), 2)

            // SKO 10302015 TKT:769 For quote line on save, make sure product, brand, vendor, and division fills from model.
            // doForm.doRS.SetFieldVal("LNK_RELATED_VE", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_VE", 2), 2)
            // doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2)
            // doForm.doRS.SetFieldVal("LNK_RELATED_BR", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_BR", 2), 2)
            // doForm.doRS.SetFieldVal("LNK_RELATED_DV", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_DV", 2), 2)

            // VS 11202015 TKT#808 : if model is CHK_WIP then Do not fill LNK_FOR_PD, LNK_RELATED_VE, LNK_RELATED_BR Details. PD needs to be entered by user manually
            doForm.doRS.ClearLinkAll("LNK_RELATED_BR");
            if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%CHK_WIP")).ToUpper()== "CHECKED")
            {
                doForm.doRS.SetFieldVal("LNK_RELATED_VE", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_VE", 2), 2);
                doForm.doRS.SetFieldVal("LNK_RELATED_BR", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_BR", 2), 2);
                doForm.doRS.SetFieldVal("LNK_RELATED_DV", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_DV", 2), 2);
            }
            else
            {
                doForm.doRS.SetFieldVal("LNK_RELATED_VE", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_VE", 2), 2);
                doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2);
                doForm.doRS.SetFieldVal("LNK_RELATED_BR", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_BR", 2), 2);
                doForm.doRS.SetFieldVal("LNK_RELATED_DV", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_DV", 2), 2);
            }

            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            par_bRunNext = false;

            // VS 11202015 TKT#808
            bool bCHKWIP = false;
            if (Convert.ToString(doRS.GetFieldVal("LNK_FOR_MO%%CHK_WIP")).ToUpper() == "CHECKED")
                bCHKWIP = true;

            decimal cResult;

            clArray doLink;
            // Dim cResult as decimal
            // Dim doQuote as object 
            // Dim lActionNo as integer
            string sFileName = "QL";
            string sSysName = "";
            DateTime dtDateTime;
            // Get QuoteInfo variable set in QT_RecOnSave
            string sQuoteInfo = Convert.ToString(goP.GetVar("QuoteInfo_" + doRS.GetFieldVal("LNK_IN_QT")));



            // If bDoNotUpdateQuoteLines remained 1 earlier due to an error, reset it
            goP.SetVar("bDoNotUpdateQuoteLines", "0");


            // ---------------- ENFORCE --------------------
            if (doRS.bBypassValidation != true)
            {
            }

            // --------- AUTO-FILLED FIELDS ---------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "SetQuoteOpenAndQLTimeCompleted"))
            {
                // Fill CHK_OPEN when Status is Open or On Hold
                switch (doRS.GetFieldVal("MLS_STATUS", 2))
                {
                    case 0:
                    case 1     // Open, On Hold (was using 0 and 20)
                   :
                        {
                            doRS.SetFieldVal("CHK_OPEN", 1, 2);
                            break;
                        }

                    case 2:
                    case 3 // Won, Lost
             :
                        {
                            doRS.SetFieldVal("CHK_OPEN", 0, 2);
                            // CS 1/11/10 Fill QL.DTE_TimeCompleted with QT.DTE_DateClosed if blank
                            if (doRS.GetFieldVal("DTT_TimeCompleted", 1) == "")
                            {
                                // If don't manage quote line status independent of qt set to same as QT; otherwise, set to now.
                                if (doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT") == "0")
                                    doRS.SetFieldVal("DTT_TimeCompleted", doRS.GetFieldVal("LNK_IN_QT%%DTT_DATECLOSED"));
                                else
                                    // QL status mgmt is on. 
                                    doRS.SetFieldVal("DTT_TimeCompleted", goTR.NowLocal());
                            }

                            break;
                        }

                    default:
                        {
                            doRS.SetFieldVal("CHK_OPEN", 0, 2);
                            break;
                        }
                }
            }


            // Fill Quote Date and Time from the linked Quote
            if (sQuoteInfo == "")
                doRS.SetFieldVal("DTT_QTETIME", doRS.GetFieldVal("LNK_IN_QT%%DTT_TIME"));
            else
                doRS.SetFieldVal("DTT_QTETIME", goTR.StrRead(sQuoteInfo, "QT_DTT_TIME", null/* Conversion error: Set to default value for this argument */, false));

            // Automatically fill Expected Close Date if blank
            if (doRS.GetFieldVal("DTE_ExpCloseDate", 1) == "")
                doRS.SetFieldVal("DTE_ExpCloseDate", "Today");



            // Old non-UTC-aware code
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillYearMonthDayFields"))
            {
                // 'Fill Month/Year fields used for grouping/totaling in reports
                // doRS.SetFieldVal("TXT_YEAR", goTR.GetYear(doRS.GetFieldVal("DTT_TIME", 2)))
                // doRS.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(doRS.GetFieldVal("DTT_TIME", 2))))
                // 'Close Month/Year fields used for grouping/totaling in reports
                // doRS.SetFieldVal("TXT_YEARCLOSE", goTR.GetYear(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2)))
                // doRS.SetFieldVal("SI__MONTHCLOSE", goTR.StringToNum(goTR.GetMonth(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2))))
                DateTime _dt = Convert.ToDateTime(doRS.GetFieldVal("DTT_TIME", 2));
                dtDateTime = goTR.UTC_LocalToUTC(ref _dt);
                doRS.SetFieldVal("TXT_YEAR", goTR.GetYear(dtDateTime));
                doRS.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(dtDateTime),"",ref par_iValid,""));
                if (goData.IsFieldValid("QL", "SI__Day"))
                {
                    doRS.SetFieldVal("SI__Day", goTR.StringToNum(goTR.GetDay(dtDateTime), "", ref par_iValid, ""));

                }
                DateTime _DT = Convert.ToDateTime(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2));
                dtDateTime = goTR.UTC_LocalToUTC(ref _DT);
                //dtDateTime = goTR.UTC_LocalToUTC(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2));
                doRS.SetFieldVal("TXT_YEARCLOSE", goTR.GetYear(dtDateTime));
                doRS.SetFieldVal("SI__MONTHCLOSE", goTR.StringToNum(goTR.GetMonth(dtDateTime), "", ref par_iValid, ""));
                if (goData.IsFieldValid("QL", "SI__DayClose"))
                {
                    doRS.SetFieldVal("SI__DayClose", goTR.StringToNum(goTR.GetDay(dtDateTime), "", ref par_iValid, ""));

                }
            }

            // CS 6/3/09 Store QL Model info in var to use instead of creating rowsets throughout.
            string sWork = "";

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before Model rowset" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            clRowSet doMO = new clRowSet("MO", 3, "GID_ID='" + doRS.GetFieldVal("LNK_FOR_MO") + "'", null/* Conversion error: Set to default value for this argument */, "CUR_COST,LNK_OF_PD,LNK_OF_PD%%LNK_RELATED_DV,LNK_RELATED_VE,TXT_UNITTEXT");
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("After model rowset" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            // VS 11202015 TKT#808 :
            clRowSet doPD = new clRowSet("PD", 3, "GID_ID='" + doRS.GetFieldVal("LNK_FOR_PD") + "'", null/* Conversion error: Set to default value for this argument */, "LNK_RELATED_DV,LNK_RELATED_VE,LNK_RELATED_BR");
            // Dim doPD As New clRowSet("PD", 3, "GID_ID='" & doRS.GetFieldVal("LNK_FOR_PD") & "'", , "LNK_RELATED_VE")

            if (doMO.GetFirst() == 1)
            {
                goTR.StrWrite(ref sWork, "MO_TXT_UNITTEXT", doMO.GetFieldVal("TXT_UNITTEXT"));
                goTR.StrWrite(ref sWork, "MO_CUR_COST", doMO.GetFieldVal("CUR_COST"));
            }

            if (bCHKWIP)
            {
                if (doPD.GetFirst() == 1)
                {
                    goTR.StrWrite(ref sWork, "MO_LNK_OF_PD", doRS.GetFieldVal("LNK_FOR_PD"));
                    goTR.StrWrite(ref sWork, "MO_LNK_RELATED_VE", doPD.GetFieldVal("LNK_RELATED_VE"));
                    goTR.StrWrite(ref sWork, "MO_LNK_OF_PD_LNK_RELATED_DV", doPD.GetFieldVal("LNK_RELATED_DV"));
                }
            }
            else if (doMO.GetFirst() == 1)
            {
                goTR.StrWrite(ref sWork, "MO_LNK_OF_PD", doMO.GetFieldVal("LNK_OF_PD"));
                goTR.StrWrite(ref sWork, "MO_LNK_RELATED_VE", doMO.GetFieldVal("LNK_RELATED_VE"));
                goTR.StrWrite(ref sWork, "MO_LNK_OF_PD_LNK_RELATED_DV", doMO.GetFieldVal("LNK_OF_PD%%LNK_RELATED_DV"));
            }
            goP.SetVar("QuoteLineInfo_" + doRS.GetFieldVal("GID_ID"), sWork);

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillUnitFromModel"))
            {
                if (doRS.GetFieldVal("TXT_UNIT") == "")
                {
                    if (sWork == "")
                        doRS.SetFieldVal("TXT_UNIT", doRS.GetFieldVal("LNK_FOR_MO%%TXT_UNITTEXT"));
                    else
                        doRS.SetFieldVal("TXT_UNIT", goTR.StrRead(sWork, "MO_TXT_UNITTEXT", null/* Conversion error: Set to default value for this argument */, false));
                }
            }


            // ---------- Set field values -----------
            if (goP.GetRunMode() != "Import")
            {
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillRedundantLinksForReporting"))
                {

                    // CS Debug
                    // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("BEfore fill redundant links" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                    // '==> AFTER GETFIELDVAL FROM 2ND HOP LINKS AND CLEARLINKALL ARE FIXED, ENABLE THIS SECTION
                    // 'AND REMOVE IT FROM FORMONSAVE SCRIPT.
                    // TraceLine("Setting links to values from the Quote and Model","",sProc)
                    doRS.ClearLinkAll("LNK_TO_CO");
                    doRS.ClearLinkAll("LNK_FOR_PD");

                    // CS 6/2/09: Get values from values set in QT_RecOnSave if it exists
                    if (sQuoteInfo == "")
                        doRS.SetFieldVal("LNK_TO_CO", doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2);
                    else
                        doRS.SetFieldVal("LNK_TO_CO", goTR.StrRead(sQuoteInfo, "QT_LNK_TO_CO", null/* Conversion error: Set to default value for this argument */, false), 1);
                    if (sWork == "")
                    {
                        // VS 11262015 TKT#808
                        if (!bCHKWIP)
                            doRS.SetFieldVal("LNK_FOR_PD", doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2);
                    }
                    else
                        doRS.SetFieldVal("LNK_FOR_PD", goTR.StrRead(sWork, "MO_LNK_OF_PD", null/* Conversion error: Set to default value for this argument */, false));


                    // TraceLine("LNK_FOR_MO%%LNK_OF_PD: '" & doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD") & "'", "", sProc)


                    // CS 8/27/08: Only set Cred/Peer User if WOP for managing Peer/Credited To User independent of 
                    // Qt is off
                    if (Convert.ToString(doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_USERMGMT")) != "1")
                    {
                        doRS.ClearLinkAll("LNK_PEER_US");
                        doRS.ClearLinkAll("LNK_CREDITEDTO_US");
                        if (sQuoteInfo == "")
                        {
                            doRS.SetFieldVal("LNK_PEER_US", doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2);
                            doRS.SetFieldVal("LNK_CREDITEDTO_US", doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2);
                        }
                        else
                        {
                            doRS.SetFieldVal("LNK_PEER_US", goTR.StrRead(sQuoteInfo, "QT_LNK_PEER_US", null/* Conversion error: Set to default value for this argument */, false), 1);
                            doRS.SetFieldVal("LNK_CREDITEDTO_US", goTR.StrRead(sQuoteInfo, "QT_LNK_CREDITEDTO_US", null/* Conversion error: Set to default value for this argument */, false), 1);
                        }
                    }

                    doRS.ClearLinkAll("LNK_RELATED_PR");
                    doRS.ClearLinkAll("LNK_RELATED_TE");
                    if (sQuoteInfo == "")
                    {
                        doRS.SetFieldVal("LNK_RELATED_PR", doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2);
                        doRS.SetFieldVal("LNK_RELATED_TE", doRS.GetFieldVal("LNK_TO_CO%%LNK_IN_TE", 2), 2);
                    }
                    else
                    {
                        doRS.SetFieldVal("LNK_RELATED_PR", goTR.StrRead(sQuoteInfo, "QT_LNK_RELATED_PR", null/* Conversion error: Set to default value for this argument */, false), 1);
                        doRS.SetFieldVal("LNK_RELATED_TE", goTR.StrRead(sQuoteInfo, "CO_LNK_IN_TE", null/* Conversion error: Set to default value for this argument */, false), 1);
                    }
                }
            }

            if (goP.GetRunMode() != "Import")
            {
                // LNK_INVOLVES_USER
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillInvolvesUser"))
                {
                    doLink = new clArray();
                    oTable = null;
                    doLink = doRS.GetLinkVal("LNK_CREDITEDTO_US", ref doLink, true, 0, -1, "A_a", ref oTable);
                    oTable = null;
                    doLink = doRS.GetLinkVal("LNK_PEER_US", ref doLink, true, 0, -1, "A_a", ref oTable);
                    // If doLink <> vbNull Then
                    doRS.SetLinkVal("LNK_INVOLVES_US", doLink);
                    // delete(doLink)
                    doLink = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }


            // Fill Division
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillDivisionFromModel"))
            {
                // TraceLine("Setting Model's Product's Division into Related Division","",sProc)
                doRS.ClearLinkAll("LNK_RELATED_DV");
                // TraceLine("LNK_FOR_MODEL: '" & doForm.GetFieldVal("LNK_FOR_MO") & "'","",sProc)
                // TraceLine("LNK_FOR_MO%%LNK_OF_PD: '" & doForm.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD") & "'","",sProc)
                // TraceLine("LNK_FOR_MO%%LNK_OF_PD%%LNK_RELATED_DV: '" & doForm.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD%%LNK_RELATED_DV") & "'","",sProc)
                // Cs commented doRS.SetFieldVal("LNK_RELATED_DV", doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD%%LNK_RELATED_DV"))
                // CS: 3 hops do not work...change to below
                if (sWork == "")
                {
                    // VS 11262015 TKT#808
                    // Dim sProduct As String = doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 1)
                    string sProduct = "";
                    if (bCHKWIP)
                    {
                        sProduct = Convert.ToString(doRS.GetFieldVal("LNK_FOR_PD", 1));

                    }
                    else
                    {
                        sProduct = Convert.ToString(doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 1));

                    }
                    // Get Products division
                    clRowSet doProduct = new clRowSet("PD", 3, "GID_ID='" + sProduct + "'", "", "LNK_Related_DV");
                    if (doProduct.GetFirst() == 1)
                    {
                        doRS.SetFieldVal("LNK_RELATED_DV", doProduct.GetFieldVal("LNK_RELATED_DV", 2), 2);

                    }
                }
                else
                {
                    doRS.SetFieldVal("LNK_RELATED_DV", goTR.StrRead(sWork, "MO_LNK_OF_PD_LNK_RELATED_DV", null/* Conversion error: Set to default value for this argument */, false));

                }
            }


            // CS Note: CUR_PriceUnit is filled in FormOnSave based on the linked Model's price. I would think that
            // code may need to be in RecordONSave too. For now I am assuming we always have a value in CUR_Price.

            // ----------- Calc Sales Tax Rate -------------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcSalesTaxRate"))
            {
                // goP.TraceLine("Calculating CUR_SalesTax", "", sProc)
                // Formula: CUR_SalesTax=([Qty] * [PriceUnitNoDisc] - [Qty] * [PriceUnitNoDisc] * [DiscPercent] / 100) * [SalesTaxPercent] / 100
                cResult = Convert.ToDecimal(doRS.GetFieldVal("CUR_PriceUnit", 2)) * Convert.ToDecimal(doRS.GetFieldVal("SR__Qty", 2));
                // goP.TraceLine("CUR_PriceUnit: '" & Convert.ToString(doRS.GetFieldVal("CUR_PriceUnit", 2)) & "'", "", sProc)
                // goP.TraceLine("SR__Qty: '" & Convert.ToString(doRS.GetFieldVal("SR__Qty", 2)) & "'", "", sProc)
                // goP.TraceLine("cResult after PriceUnit * Qty: '" & Convert.ToString(cResult) & "'", "", sProc)
                cResult = Convert.ToDecimal(cResult) - Convert.ToDecimal(cResult) * (Convert.ToDecimal(doRS.GetFieldVal("SR__DiscPercent", 2)) / (decimal)100) * (Convert.ToDecimal(doRS.GetFieldVal("SR__SalesTaxPercent", 2)) / (decimal)100);
                // goP.TraceLine("SR__SalesTaxPercent: '" & Convert.ToString(doRS.GetFieldVal("SR__SalesTaxPercent", 2)) & "'", "", sProc)
                // goP.TraceLine("cResult after full calc: '" & Convert.ToString(cResult) & "'", "", sProc)
                // goP.TraceLine("Setting Sales Tax field: '" & Convert.ToString(cResult) & "'", "", sProc)
                // goP.TraceLine("Setting in CUR_SalesTax: '" & cResult & "'", "", sProc)
                doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cResult), 2);
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcTotal"))
                // CS Adding to calculate Quote Line totals here. Will need to calculate Quote totals
                // when they are linked together.
                // CS Debug
                // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("BEfore QL CalcTotal-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "doRS");

            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before ConnectVendors-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "ConnectVendors"))
            {
                scriptManager.RunScript("Quotline_ConnectVendors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            }

            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("After connectvendors-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            // ----------- Recalc Quote totals -------------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "RecalcQuoteTotals"))
            {
                // Recalc quote only if this isn't running as a result of Quote updating Quote Lines
                if (Convert.ToString(goP.GetVar("bDoNotUpdateQuote")) != "1" & doRS.GetLinkCount("LNK_IN_QT") > 0)
                    // goP.SetVar("bDoNotUpdateQuoteLines", "1")  'not needed because now coded in CalcQuoteTotal
                    // CS Debug
                    // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before CalcQuoteTotal-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                    scriptManager.RunScript("CalcQuoteTotal",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null, Convert.ToString(doRS.GetFieldVal("LNK_in_QT%%GID_ID")), Convert.ToString(doRS.GetFieldVal("GID_ID")), Convert.ToString(doRS.GetFieldVal("CHK_Taxable", 2)), Convert.ToString(doRS.GetFieldVal("CUR_Subtotal", 2)));
            }

            goP.DeleteVar("QuoteLineInfo_" + doRS.GetFieldVal("GID_ID"));
            

            par_doCallingObject = doRS;
            return true;
        }

        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 6/4/2013 Overrides variables for QL Cost, user enters
            // total cost NOT pulled from Model
            // CS 6/3/09 Store QL Model info in var to use instead of creating rowsets throughout.
            string sWork = "";
            clRowSet doMO = new clRowSet("MO", 3, "GID_ID='" + doRS.GetFieldVal("LNK_FOR_MO") + "'", null/* Conversion error: Set to default value for this argument */, "LNK_OF_PD,LNK_OF_PD%%LNK_RELATED_DV,LNK_RELATED_VE,TXT_UNITTEXT");
            if (doMO.GetFirst() == 1)
            {
                goTR.StrWrite(ref sWork, "MO_TXT_UNITTEXT", doMO.GetFieldVal("TXT_UNITTEXT"));
                goTR.StrWrite(ref sWork, "MO_LNK_OF_PD", doMO.GetFieldVal("LNK_OF_PD"));
                goTR.StrWrite(ref sWork, "MO_LNK_RELATED_VE", doMO.GetFieldVal("LNK_RELATED_VE"));
                goTR.StrWrite(ref sWork, "MO_LNK_OF_PD_LNK_RELATED_DV", doMO.GetFieldVal("LNK_OF_PD%%LNK_RELATED_DV"));
                // goTR.StrWrite(sWork, "MO_CUR_COST", doMO.GetFieldVal("CUR_COST"))
                goTR.StrWrite(ref sWork, "MO_CUR_COST", doRS.GetFieldVal("CUR_COST"));
                goP.SetVar("QuoteLineInfo_" + doRS.GetFieldVal("GID_ID"), sWork);
            }
            doMO = null/* TODO Change to default(_) if this is not a reference type */;

            par_doCallingObject = doRS;

            return true;
        }
        public bool QT_FormAfterSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 4/26/2013 Custom fields
            par_bRunNext = false;

            // MI 4/26/07

            Form doForm = (Form)par_doCallingObject;
            string sID;
            clRowSet doRS;
            // Dim doOrigQL As clRowSet
            clRowSet doNewQL;
            clRowSet doOrigQuote;
            int i;
            bool bQLNotFound = false;
            string sQuoteOpeningMode;
            string sMessage;

            if (doForm.GetMode() == "CREATION")
            {
                if (Convert.ToString(doForm.oVar.GetVar("QT_AddQuoteLine")) == "1")
                {
                    // Redisplay the same form so the user thinks the form never went away
                    Form doFormSame = new Form("QT", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "");
                    goUI.Queue("FORM", doFormSame);
                }
                sQuoteOpeningMode = Convert.ToString(doForm.oVar.GetVar("QuoteOpeningMode"));
                switch (sQuoteOpeningMode)
                {
                    case "Duplicate":
                    case "Revision":
                        {
                            // Create Quote Lines by copying the original ones
                            sID = Convert.ToString(doForm.oVar.GetVar("QuoteOrinalQuoteID"));
                            doRS = new clRowSet("QL", clC.SELL_EDIT, "LNK_In_QT='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*");
                            // For each quote line found create a new one, linked to the new Quote
                            goP.SetVar("bDoNotUpdateQuote", "1");
                            for (i = 1; i <= doRS.Count(); i++)
                            {
                                switch (sQuoteOpeningMode)
                                {
                                    case "Duplicate":
                                        {
                                            // ---- Technique 1: copy only model-related fields ----
                                            doNewQL = new clRowSet("QL", clC.SELL_ADD, null, null, null, 0, null, null, "CRL_QL", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), null, true); // bBypassValidation
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       // doNewQL.SetFieldVal("LNK_in_QT", doForm.doRS.GetFieldVal("GID_ID"))
                                            doNewQL.SetFieldVal("LNK_For_MO", doRS.GetFieldVal("LNK_For_MO", 2), 2);
                                            // TLD 5/1/2013 This should fill with CO, Team Leader so do nothing here?
                                            // since LNK_TO_CO is not filled until QL Form On Load?
                                            // If doNewQL.GetLinkCount("LNK_CreditedTo_US") < 1 Then
                                            // 'doNewQL.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"))
                                            // doNewQL.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"))
                                            // End If
                                            // doNewQL.SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID())      'System linked - filled automatically
                                            // TLD 5/1/2013 This should fill with ME User
                                            if (doNewQL.GetLinkCount("LNK_Peer_US") < 1)
                                            {


                                                // doNewQL.SetFieldVal("LNK_Peer_US", doNewQL.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"))
                                                doNewQL.SetFieldVal("LNK_Peer_US", goP.GetMe("ID"));
                                                doNewQL.SetFieldVal("SR__LineNo", doRS.GetFieldVal("SR__LineNo", 2), 2);
                                                doNewQL.SetFieldVal("SR__Qty", doRS.GetFieldVal("SR__Qty", 2), 2);
                                                doNewQL.SetFieldVal("TXT_Unit", doRS.GetFieldVal("TXT_Unit"));
                                                doNewQL.SetFieldVal("CUR_PriceUnit", doRS.GetFieldVal("CUR_PriceUnit", 2), 2);
                                                doNewQL.SetFieldVal("SR__DiscPercent", doRS.GetFieldVal("SR__DiscPercent", 2), 2);
                                                doNewQL.SetFieldVal("CUR_Subtotal", doRS.GetFieldVal("CUR_Subtotal", 2), 2);
                                                doNewQL.SetFieldVal("CUR_DiscAddlAmt", doRS.GetFieldVal("CUR_DiscAddlAmt", 2), 2);
                                                doNewQL.SetFieldVal("CUR_PriceUnitAfterDisc", doRS.GetFieldVal("CUR_PriceUnitAfterDisc", 2), 2);
                                                doNewQL.SetFieldVal("CUR_Cost", doRS.GetFieldVal("CUR_Cost", 2), 2);
                                                doNewQL.SetFieldVal("CUR_GrossProfit", doRS.GetFieldVal("CUR_GrossProfit", 2), 2);
                                                doNewQL.SetFieldVal("CHK_Taxable", doRS.GetFieldVal("CHK_Taxable", 2), 2);
                                                doNewQL.SetFieldVal("CHK_Report", doRS.GetFieldVal("CHK_Report", 2), 2);
                                                doNewQL.SetFieldVal("CHK_Include", doRS.GetFieldVal("CHK_Include", 2), 2);
                                                doNewQL.SetFieldVal("TXT_Model", doRS.GetFieldVal("TXT_Model"));
                                                doNewQL.SetFieldVal("MMO_Details", doRS.GetFieldVal("MMO_Details"));

                                                // 'Fields filled in QL_FormOnLoadRecord:
                                                // doForm.doRS.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTT_TIME"))
                                                // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                                                // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                                                // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                                                // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                                                // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                                                // doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"))

                                                // TLD 4/26/2013 Custom fields
                                                doNewQL.SetFieldVal("MLS_Budgetary", doRS.GetFieldVal("MLS_Budgetary", 2), 2);
                                                doNewQL.SetFieldVal("SR__MultiplierUsed", doRS.GetFieldVal("SR__MultiplierUsed", 2), 2);
                                                doNewQL.SetFieldVal("SR__MarkupPerc", doRS.GetFieldVal("SR__MarkupPerc", 2), 2);
                                                doNewQL.SetFieldVal("SR__CommissionPerc", doRS.GetFieldVal("SR__CommissionPerc", 2), 2);
                                                // TLD 5/16/2013 Copy more fields
                                                doNewQL.SetFieldVal("LNK_TakenAt_LO", doRS.GetFieldVal("LNK_TakenAt_LO", 2), 2);
                                                doNewQL.SetFieldVal("LNK_OriginatedBy_CN", doRS.GetFieldVal("LNK_OriginatedBy_CN", 2), 2);
                                                // TLD 5/22/2013 Copye more
                                                doNewQL.SetFieldVal("MLS_Type", doRS.GetFieldVal("MLS_Type", 2), 2);
                                                doNewQL.SetFieldVal("MLS_Budgetary", doRS.GetFieldVal("MLS_Budgetary", 2), 2);

                                                // 'Fields filled in CRL_QL:
                                                // LNK_IN_QT=<%GID_ID%>
                                                // LNK_Related_PR=<%LNK_Related_PR%>
                                                // MLS_REASONWONLOST=<%MLS_REASONWONLOST%>
                                                // MLS_Status=<%MLS_Status%>
                                            }
                                            if (doNewQL.Commit() != 1)
                                            {
                                                goP.DeleteVar("bDoNotUpdateQuote");
                                                // CS 6/23/08 Reset var
                                                goP.SetVar("USEQTSTATUS", "");
                                                // CS 8/27/08 Reset var
                                                goP.SetVar("USEQTUSERS", "");
                                                // MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                                                goErr.SetError(35000, sProc, "Error committing an add rowset for the new Quote Line.");
                                                return false;
                                            }

                                            break;
                                        }

                                    case "Revision":
                                        {
                                            // ---- Copy all fields, then reset them as needed ------
                                            // doOrigQL = New clRowSet("QL", clC.SELL_EDIT, "GID_ID='" & doRS.GetFieldVal("GID_ID") & "'")
                                            // If doOrigQL.Count < 1 Then
                                            // 'Record not found - skip it and tell the user later
                                            // bQLNotFound = True
                                            // Else
                                            doNewQL = new clRowSet("QL", clC.SELL_ADD, null, null, null, 0, null, null, "CRU_QL", null, null, true); // True: bBypassValidation
                                            if (!goData.CopyRecord(ref doRS,ref doNewQL))
                                            {
                                                goP.DeleteVar("bDoNotUpdateQuote");
                                                // CS 6/23/08 Reset var
                                                goP.SetVar("USEQTSTATUS", "");
                                                // CS 8/27/08 Reset var
                                                goP.SetVar("USEQTUSERS", "");
                                                goErr.SetError(35000, sProc, "Error running goData.CopyRecord(). Are both rowsets in clc.SELL_EDIT mode?");
                                                return false;
                                            }
                                            else
                                            {
                                                // Link the line to the new quote
                                                doNewQL.ClearLinkAll("LNK_In_QT");
                                                doNewQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("GID_ID"), clC.SELL_SYSTEM);
                                                // Reset datetime, quote datetime
                                                doNewQL.SetFieldVal("DTT_Time", "Today|Now");
                                                doNewQL.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM), clC.SELL_SYSTEM);
                                                // Reset Status, Reason, and Completed
                                                // CS 8/3/07: Per PJ, statuses/reasons should remain as they were in original QT
                                                // doNewQL.SetFieldVal("MLS_Status", 0, clC.SELL_SYSTEM)           'Open
                                                // doNewQL.SetFieldVal("MLS_ReasonWonLost", 0, clC.SELL_SYSTEM)    '<Make selection>
                                                doNewQL.SetFieldVal("DTT_TimeCompleted", "");
                                                if (doNewQL.Commit() != 1)
                                                {
                                                    goP.DeleteVar("bDoNotUpdateQuote");
                                                    // CS 6/23/08 Reset var
                                                    goP.SetVar("USEQTSTATUS", "");
                                                    // CS 8/27/08 Reset var
                                                    goP.SetVar("USEQTUSERS", "");
                                                    // MI 3/31/09 added 35000, sproc. The string was in the first parameter.
                                                    goErr.SetError(35000, sProc, "Error committing an add rowset for the new Quote Line.");
                                                    return false;
                                                }
                                            }

                                            break;
                                        }
                                }
                                if (doRS.GetNext() != 1)
                                    break;
                            }
                            goP.DeleteVar("bDoNotUpdateQuote");

                            if (sQuoteOpeningMode == "Revision")
                            {
                                // Set Status of the original quote to Revised (6)
                                doOrigQuote = new clRowSet("QT", clC.SELL_EDIT, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*");
                                if (doOrigQuote.Count() > 0)
                                {
                                    doOrigQuote.SetFieldVal("MLS_Status", 6, clC.SELL_SYSTEM);
                                    if (doOrigQuote.Commit() != 1)
                                    {
                                        sMessage = "The Status of the original Quote can't be changed to 'Revised'. It will be reported in totals as a duplicate of the quote you just created. Please contact your Selltis administrrator. Quote Name: '" + doOrigQuote.GetFieldVal("SYS_Name") + "'. Quote ID: '" + sID + "'.";

                                    }
                                    // Change the status of all linked QLs to 'revised (6)'
                                    // Have this above: doRS = New clRowSet("QL", clC.SELL_EDIT, "LNK_In_QT='" & sID & "'")
                                    if (doRS.GetFirst() == 1)
                                    {
                                        do
                                        {
                                            doRS.SetFieldVal("MLS_STATUS", 6, 2);
                                            if (doRS.Commit() != 1)
                                            {
                                                sMessage = "The Status of one or more of the original Quote lines can't be changed to 'Revised'. Please contact your Selltis administrator. Quote Name: '" + doOrigQuote.GetFieldVal("SYS_NAME") + "'. Quote ID: '" + sID + "'.";

                                            }
                                            if (doRS.GetNext() == 0)
                                                break;
                                        }
                                        while (true);
                                    }
                                }
                            }

                            // Recalc the new quote
                            scriptManager.RunScript("CalcQuoteTotal",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections, null, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));

                            if (Convert.ToString(doForm.oVar.GetVar("QuoteDuplicateManageLines")) == "1")
                            {
                                // Redisplay the Quote form
                                Form doFormSame = new Form("QT", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "");
                                if (bQLNotFound)
                                    doFormSame.MessageBox("One or more Quote Lines from the original Quote couldn't be created because they don't exist. They may have been deleted by another user.");
                                doFormSame.MessagePanel("Click the buttons next to the Lines linkbox to create, edit, or remove the lines.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "info.gif");
                                goUI.Queue("FORM", doFormSame);
                            }

                            break;
                        }
                }
            }

            // CS 6/23/08 Reset var
            goP.SetVar("USEQTSTATUS", "");
            // CS 8/27/08
            goP.SetVar("USEQTUSERS", "");

            // CS 7/29/09 Reset variable that told us we had opened a QT in a form. This variable is set in QT_formOnloadrecord and is used in
            // QT_RecordOnSave to tell us that we need to call RecalcTotals if all we did was edit a QT via rowset (not from a form), and not edit QLS
            goP.SetVar("OpenQTForm", "");
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_LNK_To_CO_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/1/2013 Set Credited to User
            doForm.doRS.ClearLinkAll("LNK_CreditedTo_US");
            doForm.doRS.SetFieldVal("MLS_Type", 0, 2);
            if (doForm.doRS.IsLinkEmpty("LNK_To_CO") != true)
            {
                // TLD 5/22/2013 Mod to get RS since adding another field to copy
                clRowSet doCORS = new clRowSet("CO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_To_CO") + "'", null/* Conversion error: Set to default value for this argument */, "LNK_TeamLeader_US, MLS_Type", 1);
                if (doCORS.GetFirst() == 1)
                {
                    // TLD 5/22/2013 Mod for CO RS, add Type
                    // Type should match, but just in case
                    // doForm.doRS.SetFieldVal("LNK_CreditedTo_US", doForm.doRS.GetFieldVal("LNK_To_CO%%LNK_TeamLeader_US", 2), 2)
                    doForm.doRS.SetFieldVal("LNK_CreditedTo_US", doCORS.GetFieldVal("LNK_TeamLeader_US", 2), 2);
                    clList goList = new clList();
                    string sCOType = Convert.ToString(doCORS.GetFieldVal("MLS_Type", 1));
                    // --TLD 5/29/2013 Oops -- changed from "AC:TYPE" to "CO:TYPE"
                    // Dim sType As String = goList.LReadSeek("AC:TYPE", "VALUE", sCOType) 'Find index number of web submission status in Ac type list
                    string sType = goList.LReadSeek("CO:TYPE", "VALUE", sCOType); // Find index number of web submission status in Ac type list
                                                                                  // CS 8/11/11: If type looking for does not exist, the default MLS value is returned so need to 
                                                                                  // check if returned that error and if so, skip this.
                                                                                  // If sType <> "" Then 'Found web sub type
                    if (sType != "" & goErr.GetLastError("NUMBER") != "E30035")
                    {
                        doForm.doRS.SetFieldVal("MLS_Type", goTR.StringToNum(sType,"",ref par_iValid,""), 2);

                    }
                }
            }
            par_doCallingObject = doForm;

            return true;
        }


        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/1/2013 ----------Set Control States
            doForm.SetControlState("CUR_ValueIndex", 4);

            // TLD 5/1/2013 Fill Credted To User with CO Team Leader
            // on creation
            if (doForm.GetMode() == "CREATION")
            {
                doForm.doRS.ClearLinkAll("LNK_CreditedTo_US");
                // TLD 5/22/20113 Add/Clear MLS_Type
                doForm.doRS.SetFieldVal("MLS_Type", 0, 2);
                if (doForm.doRS.IsLinkEmpty("LNK_To_CO") != true)
                {
                    // TLD 5/22/2013 Mod to get RS since adding another field to copy
                    clRowSet doCORS = new clRowSet("CO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_To_CO") + "'", null/* Conversion error: Set to default value for this argument */, "LNK_TeamLeader_US, MLS_Type", 1);
                    if (doCORS.GetFirst() == 1)
                    {
                        // TLD 5/22/2013 Mod for CO RS, add Type
                        // Type should match, but just in case
                        // doForm.doRS.SetFieldVal("LNK_CreditedTo_US", doForm.doRS.GetFieldVal("LNK_To_CO%%LNK_TeamLeader_US", 2), 2)
                        doForm.doRS.SetFieldVal("LNK_CreditedTo_US", doCORS.GetFieldVal("LNK_TeamLeader_US", 2), 2);
                        clList goList = new clList();
                        string sCOType = Convert.ToString(doCORS.GetFieldVal("MLS_Type", 1));
                        // TLD 5/29/2013 Oops -- changed "AC:TYPE" to "CO:TYPE"
                        // Dim sType As String = goList.LReadSeek("AC:TYPE", "VALUE", sCOType) 'Find index number of web submission status in Ac type list
                        string sType = goList.LReadSeek("CO:TYPE", "VALUE", sCOType); // Find index number of web submission status in Ac type list
                                                                                      // CS 8/11/11: If type looking for does not exist, the default MLS value is returned so need to 
                                                                                      // check if returned that error and if so, skip this.
                                                                                      // If sType <> "" Then 'Found web sub type
                        if (sType != "" & goErr.GetLastError("NUMBER") != "E30035")
                        {
                            doForm.doRS.SetFieldVal("MLS_Type", goTR.StringToNum(sType, "", ref par_iValid, ""), 2);

                        }
                    }
                }
            }
            else
            // TLD 6/4/2013 Open to Journal tab
            {
                doForm.MoveToTab(2);// Journal
            }

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("LNK_FORLINE_PD", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }
            Refresh_QuoteTotal(doForm.doRS);
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            // VS 09092015 TKT#706 : Update Last Date for Company
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doRS;
            return true;
        }

        public bool Quote_EmailSubject_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections,  clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // CS 10/16/07 Created.
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Purpose: Fills the subject of the email when sending a QT via email
            // This function is called by WebService/Rowset.asmx when a PC Link send job
            // to send a Quote via email is processed.
            // It can be customized the same as all other scripts by creating a _Pre script and
            // setting par_bRunNext=False

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 8/19/2013
            par_bRunNext = false;

            clRowSet doRS = (clRowSet)HttpContext.Current.Session["WS_RS_" + HttpContext.Current.Session.SessionID];
            string sSubject;
            string sQTNo = Strings.Trim(Convert.ToString(doRS.GetFieldVal("TXT_QuoteNo")));
            string sQTRefNo = Strings.Trim(Convert.ToString(doRS.GetFieldVal("TXT_RFQNo")));

            // Check if quote # field is filled
            if (sQTNo != "")
            {
                if (sQTRefNo != "")
                    // sSubject = "Quote #" & doRS.GetFieldVal("TXT_QUOTENO", clC.SELL_FRIENDLY) & " from " & doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) & " by " & goP.GetUserName
                    sSubject = "Quote #" + sQTNo + " from " + doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) + " regarding " + doRS.GetFieldVal("LNK_To_CO%%TXT_CompanyName") + ", " + doRS.GetFieldVal("TXT_RFQNo");
                else
                    sSubject = "Quote #" + sQTNo + " from " + doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) + " regarding " + doRS.GetFieldVal("LNK_To_CO%%TXT_CompanyName");
            }
            else if (sQTRefNo != "")
            {
                // sSubject = "Quote from " & doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) & " by " & goP.GetUserName
                sSubject = "Quote from " + doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) + " regarding " + doRS.GetFieldVal("LNK_To_CO%%TXT_CompanyName") + ", " + doRS.GetFieldVal("TXT_RFQNo");
            }
               
            else
            {
                sSubject = "Quote from " + doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) + " regarding " + doRS.GetFieldVal("LNK_To_CO%%TXT_CompanyName");

            }

            par_oReturn = sSubject;
            par_doCallingObject = doRS;
            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/4/2013 User may enter total CUR_Cost
            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)
            if (par_s1 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;

            }
            else
            {
                doForm = (Form)par_doCallingObject;

            }

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            decimal cPriceUnit;
            double rQtyFld;
            double rDiscPerc;
            decimal cDiscAddAmt;
            decimal cCostVal;
            decimal cWork;
            decimal cSubtotal;
            double rSalesTaxPerc;
            decimal cSKUCost;
            string sSKUCost;
            // TLD 6/7/2013
            double rMarkupPerc;

            // PURPOSE:
            // Calc Subtotal if Include is checked, otherwise enter 0 as subtotal
            // Field 'Price Unit No Disc' = Unit Price before discount
            // Field 'Price Unit' = Unit Price after discount
            // RETURNS:
            // True.

            // goP.TraceLine("", "", sProc)

            // CS Need to check if coming from RecOnSave b/c in that case we are working with a rowset and 
            // otherwise we are on a form.
            if (par_s1 != "doRS")
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_INCLUDE", 2)) != 1)
                {
                    // Set calculated fields to 0
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                    // TLD 6/4/2013 Don't clear cost, user may enter total cost
                    // doForm.doRS.SetFieldVal("CUR_COST", 0, 2)
                    doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_SALESTAX", 0, 2);
                }
                else
                {
                    cPriceUnit = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2));
                    rQtyFld = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                    rDiscPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__DISCPERCENT", 2));
                    cDiscAddAmt = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_DISCADDLAMT", 2));
                    // TLD 6/4/2013 pull Model cost if cost is 0 otherwise
                    // User entered unit cost
                    // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                    cCostVal = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_COST", 2));
                    if (cCostVal == 0)
                    {
                        // TLD 6/7/2013 User did NOT enter unit cost
                        sSKUCost = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST"));
                        if (sSKUCost != "")
                            cSKUCost = goTR.StringToCurr(sSKUCost,"",ref par_iValid,"");
                        else
                            cSKUCost = 0;
                        cCostVal = cSKUCost;
                    }
                    // cCostVal = cCostVal * rQtyFld
                    // TLD 6/15/2009 -- don't set CUR_COST, entered by user
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)


                    // Copy Cost from SKU and multiply it by Qty if the user edited Qty or if Qty is 0
                    // (we set it to 0 above if it is blank)
                    // bUpdCostVal is set to 1 in ControlOnLeave script for 'For Model'.

                    // Check if form variable has been set otherwise get an error comparing srQtyEnterVal as 
                    // a string ("") to a double (rQtyFld)
                    // Per MI, we can get rid of the form var dealing with changing the qty. We will always
                    // recalc regardless of it.
                    // If doForm.oVar.GetVar("srQtyEnterVal") <> "" Then
                    // If (rQtyFld <> doForm.oVar.GetVar("srQtyEnterVal")) Or (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // If doForm.oVar.Getvar("bUpdCostVal") <> "" Then
                    // If (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // CS: Moving this to QL_FormControlOnChange_USEMODELPRICE. When the user clicks this button the linked model's cost
                    // will be used.
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    // If sSKUCost <> "" Then 'CS added this If b/c if coming from QL_FormOnLoad the value
                    // 'is blank for cost and get error
                    // cSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")

                    // cCostVal = cSKUCost * rQtyFld
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
                    // End If
                    // End If

                    // TLD 6/7/2013 Calculate Markup %
                    // SGR 09252015 TKT#:733 Commented below line of code(6 lines)
                    // SGR 10262015 TKT#:733 UnCommented below lines of code(6 lines) for calculating margin if price unit or cost is not zero.
                    if (cPriceUnit != 0)
                    {
                        decimal price = Convert.ToDecimal(cPriceUnit) - Convert.ToDecimal(cCostVal);
                        rMarkupPerc = Convert.ToDouble(price) / ((double)cPriceUnit) * 100;
                        doForm.doRS.SetFieldVal("SR__MarkupPerc", rMarkupPerc, 2);
                    }
                    else
                        doForm.doRS.SetFieldVal("SR__MarkupPerc", 0, 2);

                    // Calculate unit price after discount
                    cWork = cPriceUnit - (Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rDiscPerc) / 100);
                    doForm.doRS.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                    // Calculate Subtotal
                    cSubtotal = Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld) - Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld * rDiscPerc / 100) + cDiscAddAmt;
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                    // TLD 6/7/2013 Custom Calc Gross Profit
                    // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                    // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS 6/13/07: Added rQtyField per DF
                    // cWork = cSubtotal - cCostVal
                    cWork = Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld) - Convert.ToDecimal(cCostVal) * Convert.ToDecimal(rQtyFld);

                    // SGR 09232015 TKT#:733 
                    double rCustomPerc = 0;
                    rCustomPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__CommissionPerc", 2));
                    if (rCustomPerc != 0)
                    {
                        // SGR 09252015 TKT#:733 
                        rMarkupPerc = rCustomPerc;

                        doForm.doRS.SetFieldVal("SR__MarkupPerc", rCustomPerc, 2);
                        cWork = Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld * rCustomPerc) / 100;
                        doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));
                    }
                    else
                        doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));


                    // Sales tax
                    if (Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_TAXABLE", 2)))
                    {
                        // CS 6/2/09: Get value from variable if set
                        if (goP.GetVar("QuoteInfo").ToString() == "")
                            rSalesTaxPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        else
                            rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT"),"",ref par_iValid,"");


                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", Convert.ToDecimal(cSubtotal) * Convert.ToDecimal(rSalesTaxPerc) / 100);
                    }
                    else
                    {
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", 0);
                    }
                }
            }
            else if (Convert.ToInt32(doRS1.GetFieldVal("CHK_INCLUDE", 2)) != 1)
            {
                // Set calculated fields to 0
                doRS1.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                doRS1.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                // TLD 6/4/2013 User may enter total cost
                // doRS1.SetFieldVal("CUR_COST", 0, 2)
                doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                doRS1.SetFieldVal("CUR_SALESTAX", 0, 2);
            }
            else
            {
                cPriceUnit = Convert.ToDecimal(doRS1.GetFieldVal("CUR_PRICEUNIT", 2));
                rQtyFld = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                rDiscPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__DISCPERCENT", 2));
                cDiscAddAmt = Convert.ToDecimal(doRS1.GetFieldVal("CUR_DISCADDLAMT", 2));
                // TLD 6/4/2013 pull Model cost if cost is 0 otherwise
                // User entered unit cost
                // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                cCostVal = Convert.ToDecimal(doRS1.GetFieldVal("CUR_COST", 2));
                if (cCostVal == 0)
                {
                    // TLD 6/7/2013 User did NOT enter unit cost
                    sSKUCost = Convert.ToString(doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST"));
                    if (sSKUCost != "")
                        cSKUCost = goTR.StringToCurr(sSKUCost,"",ref par_iValid,"");
                    else
                        cSKUCost = 0;
                    cCostVal = cSKUCost;
                }
                // cCostVal = cCostVal * rQtyFld

                // SGR 09252015 Commented 4 lines of code
                // TLD 6/7/2013 Calculate Markup %
                // SGR 10302015 UnCommented 4 lines of code
                if (cPriceUnit != 0)
                {
                    decimal price = Convert.ToDecimal(cPriceUnit) - Convert.ToDecimal(cCostVal);
                    rMarkupPerc = Convert.ToDouble(price) / ((double)cPriceUnit) * 100;
                    doRS1.SetFieldVal("SR__MarkupPerc", rMarkupPerc, 2);
                }
                else
                    doRS1.SetFieldVal("SR__MarkupPerc", 0, 2);

                // Calculate unit price after discount
                cWork = cPriceUnit - Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rDiscPerc / 100);
                doRS1.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                // Calculate Subtotal
                cSubtotal = Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld) - Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld * rDiscPerc / 100) + cDiscAddAmt;
                doRS1.SetFieldVal("CUR_SALESTAX", goTR.RoundCurr(cSubtotal));

                // TLD 6/7/2013 Custom Calc Gross Profit
                // cWork = cSubtotal - cCostVal
                // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS Added rQtyField 
                cWork = Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld) - Convert.ToDecimal(cCostVal) * Convert.ToDecimal(rQtyFld);
                // SGR 10302015 TKT#:733 
                double rCustomPerc = 0;
                rCustomPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__CommissionPerc", 2));
                if (rCustomPerc != 0)
                {
                    // SGR 09252015 TKT#:733 
                    rMarkupPerc = rCustomPerc;

                    doRS1.SetFieldVal("SR__MarkupPerc", rCustomPerc, 2);
                    cWork = Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld * rCustomPerc) / 100;
                    doRS1.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));
                }
                else
                    doRS1.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));


                // Sales tax
                if (Convert.ToBoolean(doRS1.GetFieldVal("CHK_TAXABLE", 2)))
                {
                    // CS 6/2/09:
                    if (goP.GetVar("QuoteInfo").ToString() == "")
                    {
                        // CS 10/19/10: Check if have a linked QT. Should always, but causes error if not.
                        if (doRS1.GetLinkCount("LNK_IN_QT") > 0)
                            rSalesTaxPerc = Convert.ToDouble(doRS1.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        else
                            rSalesTaxPerc = 0;
                    }
                    else
                        rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT"),"",ref par_iValid,"");

                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                    doRS1.SetFieldVal("CUR_SALESTAX", Convert.ToDecimal(cSubtotal) * Convert.ToDecimal(rSalesTaxPerc) / 100);
                }
                else
                {
                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0);
                    doRS1.SetFieldVal("CUR_SALESTAX", 0);
                }
            }
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            if (par_s1 == "doRS")
            {
                par_doCallingObject= doRS1;

            }
            else
            {
                par_doCallingObject = doForm;

            }
            return true;
        }
        public bool Quotline_FillItem_PreInactive(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/20/2013 Marked inactive, now they want it back to default!
            // TLD 6/7/2013 Prevent main from running
            // fill txt_model with model name instead of description
            // par_bRunNext = False

            // goP.TraceLine("", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // PURPOSE:
            // Fill the TXT_MODEL field
            // RETURNS:
            // True.

            // CS: Original code 10/10/07
            // If Trim(doForm.dors.GetFieldVal("TXT_MOdel")) = "" Then
            // If doForm.dors.GetLinkCount("LNK_FOR_MO") > 0 Then
            // 'PJ 10/12/01 Remmed end of line per BKG request
            // 'MI 4/2/08: MMO_Description is replaced with TXT_Description as of 4/3/08.
            // 'If you reenable this code, you MUST test:
            // If goData.IsFieldValid("MO", "TXT_Description") Then
            // '-> Use TXT_Description field's value 
            // Else
            // '-> Use MMO_Description
            // End If
            // doForm.dors.SetFieldVal("TXT_MODEL", doForm.dors.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION")) '& " - " & doForm.GetFieldVal("LNK_FOR_MO%%TXT_MODELNAME")
            // End If
            // End If

            string sSKUPrice = default(string);
            decimal cSKUPrice =default(decimal);
            string sSKUCost;
            decimal cSKUCost;
            decimal rQtyFld;
            decimal cCostVal;
            string sModel;
            string sLinkedModel = "";
            string sUnit;
            clRowSet doRSModel;


            // If no model selected, return
            if (doForm.doRS.GetLinkCount("LNK_FOR_MO") == 0)
                return true;

            // TLD 6/7/2013 Changed to Model Name
            // If goData.IsFieldValid("MO", "TXT_Description") Then
            // 'Cs 6/22/09
            // doRSModel = New clRowSet("MO", 3, "GID_ID='" & doForm.doRS.GetFieldVal("LNK_FOR_MO") & "'", , "TXT_Description,txt_unittext,lnk_of_pd,cur_price,cur_cost")
            // Else 'MMO_description field
            // doRSModel = New clRowSet("MO", 3, "GID_ID='" & doForm.doRS.GetFieldVal("LNK_FOR_MO") & "'", , "MMO_Description,txt_unittext,lnk_of_pd,cur_price,cur_cost")
            // End If
            doRSModel = new clRowSet("MO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_FOR_MO") + "'", null, "txt_modelname,txt_unittext,lnk_of_pd,cur_price,cur_cost");

            if (doRSModel.GetFirst() != 1)
                return true;

            // Set Unit price to linked model's price
            // sSKUPrice = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 1) '6/22/09
            sSKUPrice = Convert.ToString(doRSModel.GetFieldVal("CUR_PRICE", 1));
            if (sSKUPrice != "")
                cSKUPrice = Convert.ToDecimal(sSKUPrice);

            doForm.doRS.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2);

            // Set Cost to linked model's cost
            // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 1) 'Cs 6/22/09
            sSKUCost = Convert.ToString(doRSModel.GetFieldVal("CUR_COST", 1));
            if (sSKUCost != "")
            {
                cSKUCost = Convert.ToDecimal(sSKUCost);
                rQtyFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("SR__QTY"));
                cCostVal = cSKUCost * rQtyFld;
                doForm.doRS.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal));
            }

            // TLD 6/7/2013 Changed to get TXT_ModelName
            // If txt_model is blank set to linked model's description
            sModel = Convert.ToString(doForm.doRS.GetFieldVal("TXT_Model"));
            if (sModel == "")
                // 'MI 4/2/08 MO.TXT_Description replaces MMO_Description as of 4/3/08, but MMO_Description remains in existing DBs.
                // If goData.IsFieldValid("MO", "TXT_Description") Then
                // 'Cs 6/22/09                
                // sLinkedModel = doRSModel.GetFieldVal("TXT_Description", 1)
                // Else
                // 'sLinkedModel = doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION", 1)
                // sLinkedModel = doRSModel.GetFieldVal("MMO_DESCRIPTION", 1)
                // End If
                // doForm.doRS.SetFieldVal("TXT_Model", sLinkedModel)
                doForm.doRS.SetFieldVal("TXT_Model", doRSModel.GetFieldVal("TXT_ModelName", 1));

            // Fill unit with linked model's unit
            // sUnit = doForm.doRS.GetFieldVal("LNK_FOR_MO%%txt_unittext") '6/22/09
            sUnit = Convert.ToString(doRSModel.GetFieldVal("txt_unittext"));
            doForm.doRS.SetFieldVal("TXT_Unit", sUnit);

            scriptManager.RunScript("Quotline_CheckTaxable", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            scriptManager.RunScript("Quotline_ConnectVendors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);        // runs CalcTotal
                                                                  // goScr.RunScript("Quotline_FillItem", doForm)


            // Set Product link to Model's Product
            doForm.doRS.ClearLinkAll("LNK_FOR_PD");
            // doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2) 'cs 6/22/09
            doForm.doRS.SetFieldVal("LNK_FOR_PD", doRSModel.GetFieldVal("LNK_OF_PD", 2), 2);

            par_doCallingObject = doForm;
            return true;
        }


        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS 05252016 TKT#1104 : Copy values
            string sLevel = Convert.ToString(doRS.GetFieldVal("MLS_LEVEL"));
            int iLevel = 0;
            if (!Information.IsNumeric(sLevel))
            {
                iLevel = 0;

            }
            else
            {
                iLevel = Convert.ToInt32(sLevel);

            }
            doRS.SetFieldVal("SR__LEVELTOTAL", iLevel, 2);
            clArray doUS = (clArray)doRS.GetFieldVal("LNK_ASSIGNEDTO_US", 2);
            if (doUS.GetDimension() > 0)
            {
                doRS.SetFieldVal("LNK_PRIMARY_US", doUS.GetItem(1));

            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool Utility_RunImportUtility(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                goUI.OpenURLExternal("../Pages/cus_diaImportMan.aspx", "Selltis", "height=840,width=1250,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
            }


            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);

                }
            }

            return true;
        }
        public bool UpdateLastDateinCO(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // VS 09092015 TKT#706 : Update Last Date for Company
            // Purpose Update LastDate for OP, QT, LEAD, AC, AC Sales and also in CO

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sFile = doRS.GetFileName();
            string sField = "";       // field to update in company/contact
            string sFieldLabel = "";
            DateTime dDate;
            string sCOLinkFieldName = "";

            // Need to update only for new records created
            if (doRS.GetInfo("TYPE") == "2")
            {
                if (sFile == "AC")
                {
                    sCOLinkFieldName = "LNK_RELATED_CO";
                    // If Activity is of type Sales Visit, update linked Related Company.Last AC Sales date
                    if (Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 11)
                    {
                        sField = "DTT_LASTACSALES";

                    }
                    else
                    {
                        // Activity is not of type sales visit
                        sField = "DTT_LASTAC";
                    }
                        

                    if (Convert.ToInt32(doRS.GetFieldVal("MLS_PURPOSE", 2)) == 8)
                    {
                        sField = "DTT_LASTLEAD";

                    }
                }
                else if (sFile == "OP")
                {
                    sCOLinkFieldName = "LNK_FOR_CO";
                    sField = "DTT_LASTOP";
                }
                else if (sFile == "QT")
                {
                    sCOLinkFieldName = "LNK_TO_CO";
                    sField = "DTT_LASTQT";
                }

                dDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_CreationTime", 2));

                if (sField != "")
                {
                    sFieldLabel = goData.GetFieldLabel("CO", sField);
                    // Update CO Record
                    if (doRS.GetLinkCount(sCOLinkFieldName) > 0)
                    {
                        clArray doCompanies = new clArray();
                        doCompanies = (clArray)doRS.GetFieldVal(sCOLinkFieldName, 2);
                        // Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doCompanies.GetDimension(); i++)
                        {
                            clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", null, sField, 1, null, null, null, null, null, true, true);
                            if (doRSCompany.GetFirst()==1)
                            {
                                doRSCompany.SetFieldVal(sField, dDate, 2);
                                if (doRSCompany.Commit() == 0)
                                    goLog.Log(sProc, "CO update of last " + sFieldLabel + " field failed for CO " + doRSCompany.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", -1, false, true);
                            }
                            doRSCompany = null/* TODO Change to default(_) if this is not a reference type */;
                        }
                    }
                }
            }

            return true;
        }

        public bool AutoCOUpdate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 09092015 TKT#706 : Update Last Date for Company
            // AGE_D0D32818-BC80-4333-5858-A09800E45CDE
            // PURPOSE:
            // Called by agent to update 3 custom date fields that
            // record latest AC of type Sales Visit, latest OP and latest Quote
            // A_01_EXECUTE=AutoCOUpdate
            // A_01_OBJSHARED = 1
            // A_01_TYPE = RUNSCRIPT
            // A_ORDER=1,
            // ACTIONS=1,
            // ACTIVE = 1
            // E_TM_HOUR = 19 '7:00 PM
            // E_TM_INTERVAL = 3 'Every day
            // E_TM_MINUTE = 0 'top of hour
            // EVENT=TIMER
            // US_NAME=AutoCOUpdate
            // US_PURPOSE=Runs daily Update of Company records
            // SORTVALUE1=TIMER_ACTIVE

            long lBiID = 0;
            long lastBiId = 0;
            clRowSet doRS;
            clRowSet doNewRS;
            long iCount = 0;
            bool bUpdateCO = false;
            string par_sDelim = " ";
            string sNowDate = goTR.DateTimeToSysString(goTR.NowLocal(),ref par_iValid,ref par_sDelim);
            string sRecID = "";
            string sWOP = goMeta.PageRead("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED");
            int iFailedOther = 0;
            int iFailedPerm = 0;
            int iFailedTotal = 0;
            int iSuccess = 0;

            try
            {
                do
                {
                    doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", 50, null, null, null, null, null, true, true, true);
                    iCount = iCount + doRS.Count();

                    if (doRS.GetFirst() == 1)
                    {
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));

                        
                        do
                        {
                            bUpdateCO = false;
                            sRecID = (doRS.GetCurrentRecID() == null) ? "" : (doRS.GetCurrentRecID().ToString());

                            

                            // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                            if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                            {
                                doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            // TLD 5/18/2011 -----------Added to include date of latest OP
                            if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                            {
                                doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            // TLD 5/18/2011 -----------Added to include date of latest QT
                            if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                            {
                                doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }

                            // Update CO
                            if (bUpdateCO == true)
                            {
                                if (doRS.Commit() == 0)
                                {
                                    if (goErr.GetLastError("NUMBER") == "E47250")
                                    {
                                        // Commit failed b/c user has no permissions to edit record; log it, but proceed
                                        iFailedPerm = iFailedPerm + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + doRS.GetFieldVal("TXT_CompanyName") + " due to permissions.", -1, false, true);
                                    }
                                    else
                                    {
                                        // Commit failed for some other reason.
                                        iFailedOther = iFailedOther + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + doRS.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", -1, false, true);
                                    }
                                }
                            }

                            if (doRS.GetNext() == 0)
                                break;
                        }
                        while (true);
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));

                    }
                    else
                        break;
                }
                while (true)// until set to true below// testing// testing// get last BI__ID processed
        ;

                // Check once more for any newly added records
                doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", 1, null, null, null, null, null, true, true, true);
                iCount = iCount + doRS.Count();
                if (doRS.GetFirst() == 1)
                {
                    do
                    {
                        bUpdateCO = false;
                        sRecID = (doRS.GetCurrentRecID() == null) ? "" : (doRS.GetCurrentRecID().ToString());


                        // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                        if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                        {
                            doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst()== 1)
                            {
                                doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        // TLD 5/18/2011 -----------Added to include date of latest OP
                        if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                        {
                            doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        // TLD 5/18/2011 -----------Added to include date of latest QT
                        if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                        {
                            doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }

                        // Update CO
                        if (bUpdateCO == true)
                        {
                            if (doRS.Commit() == 0)
                            {
                                if (goErr.GetLastError("NUMBER") == "E47250")
                                {
                                    // Commit failed b/c user has no permissions to edit record; log it, but proceed
                                    iFailedPerm = iFailedPerm + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + doRS.GetFieldVal("TXT_CompanyName") + " due to permissions.", -1, false, false,0,0) ;
                                }
                                else
                                {
                                    // Commit failed for some other reason.
                                    iFailedOther = iFailedOther + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + doRS.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".",1,false,false,0);
                                }
                            }
                        }

                        if (doRS.GetNext() == 0)
                            break;
                    }
                    while (true)// until set to true below// testing// testing
        ;
                    lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    // get last bi__id processed        
                }
                iFailedTotal = iFailedOther + iFailedPerm;
                iSuccess = Convert.ToInt32(iCount) - iFailedTotal;

                // Write to WOP
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Started " + sNowDate + " and Completed " + goTR.DateTimeToSysString(goTR.NowLocal(),ref par_iValid,ref par_sDelim) + " with " + iSuccess + " successful updates; " + iFailedPerm + " failed updates due to permissions; " + iFailedOther + " total failed updates.");
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);

                iCount = 0;
                iFailedOther = 0;
                iFailedPerm = 0;
                iFailedTotal = 0;
                iSuccess = 0;
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                lBiID = 0;
            }
            catch (Exception ex)
            {
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Failed at Record " + sRecID + " " + goErr.GetLastError("NUMBER"));
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);
            }
            //par_doCallingObject = doNewRS;
            return true;
        }
        public bool Quotline_FillItem_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 11202015 TKT#808 : if model is CHK_WIP then Do not fill LNK_FOR_PD, LNK_RELATED_VE, LNK_RELATED_BR Details. PD needs to be entered by user manually
            par_bRunNext = false;
            // goP.TraceLine("", "", sProc)

            Form doForm =(Form)par_doCallingObject;

            // PURPOSE:
            // Fill the TXT_MODEL field
            // RETURNS:
            // True.

            // CS: Original code 10/10/07
            // If Trim(doForm.dors.GetFieldVal("TXT_MOdel")) = "" Then
            // If doForm.dors.GetLinkCount("LNK_FOR_MO") > 0 Then
            // 'PJ 10/12/01 Remmed end of line per BKG request
            // 'MI 4/2/08: MMO_Description is replaced with TXT_Description as of 4/3/08.
            // 'If you reenable this code, you MUST test:
            // If goData.IsFieldValid("MO", "TXT_Description") Then
            // '-> Use TXT_Description field's value 
            // Else
            // '-> Use MMO_Description
            // End If
            // doForm.dors.SetFieldVal("TXT_MODEL", doForm.dors.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION")) '& " - " & doForm.GetFieldVal("LNK_FOR_MO%%TXT_MODELNAME")
            // End If
            // End If

            string sSKUPrice;
            decimal cSKUPrice=default(decimal);
            string sSKUCost;
            decimal cSKUCost;
            decimal rQtyFld;
            decimal cCostVal;
            string sModel;
            string sLinkedModel = "";
            string sUnit;
            clRowSet doRSModel;


            // If no model selected, return
            if (doForm.doRS.GetLinkCount("LNK_FOR_MO") == 0)
            {
                return true;

            }

            if (goData.IsFieldValid("MO", "TXT_Description"))
            {
                // Cs 6/22/09
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_FOR_MO") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_Description,CHK_WIP,txt_unittext,lnk_of_pd,cur_price,cur_cost");
            }
                
            else
            {
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_FOR_MO") + "'", null/* Conversion error: Set to default value for this argument */, "MMO_Description,CHK_WIP,txt_unittext,lnk_of_pd,cur_price,cur_cost");

            }

            if (doRSModel.GetFirst() != 1)
            {
                return true;

            }

            // Set Unit price to linked model's price
            // sSKUPrice = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 1) '6/22/09
            cSKUPrice = Convert.ToDecimal(doRSModel.GetFieldVal("CUR_PRICE", 2));
            //if (sSKUPrice != "")
            //    cSKUPrice = Convert.ToDecimal(sSKUPrice);

            doForm.doRS.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2);

            // Set Cost to linked model's cost
            // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 1) 'Cs 6/22/09
            cSKUCost = Convert.ToDecimal(doRSModel.GetFieldVal("CUR_COST", 2));
            if (cSKUCost != 0)
            {
                //cSKUCost = Convert.ToDecimal(sSKUCost);
                rQtyFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("SR__QTY"));
                cCostVal = cSKUCost * rQtyFld;
                doForm.doRS.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal));
            }

            // If txt_model is blank set to linked model's description
            sModel = Convert.ToString(doForm.doRS.GetFieldVal("TXT_Model"));
            if (sModel == "")
            {
                // MI 4/2/08 MO.TXT_Description replaces MMO_Description as of 4/3/08, but MMO_Description remains in existing DBs.
                if (goData.IsFieldValid("MO", "TXT_Description"))
                    // Cs 6/22/09                
                    sLinkedModel = Convert.ToString(doRSModel.GetFieldVal("TXT_Description", 1));
                else
                    // sLinkedModel = doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION", 1)
                    sLinkedModel = Convert.ToString(doRSModel.GetFieldVal("MMO_DESCRIPTION", 1));
                doForm.doRS.SetFieldVal("TXT_Model", sLinkedModel);
            }

            // Fill unit with linked model's unit
            // sUnit = doForm.doRS.GetFieldVal("LNK_FOR_MO%%txt_unittext") '6/22/09
            sUnit = Convert.ToString(doRSModel.GetFieldVal("txt_unittext"));
            doForm.doRS.SetFieldVal("TXT_Unit", sUnit);

            scriptManager.RunScript("Quotline_CheckTaxable", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            // goScr.RunScript("Quotline_FillItem", doForm)

            if  (Convert.ToString(doRSModel.GetFieldVal("CHK_WIP")).ToUpper() == "UNCHECKED")
            {
                scriptManager.RunScript("Quotline_ConnectVendors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                // Set Product link to Model's Product
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                // doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2) 'cs 6/22/09
                doForm.doRS.SetFieldVal("LNK_FOR_PD", doRSModel.GetFieldVal("LNK_OF_PD", 2), 2);
            }

            scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);        // runs CalcTotal

            return true;
        }



        public bool Quotline_ConnectVendors_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            par_bRunNext = false;
            // CS 8/8/11: No longer fill QL VE from linked Company

            // Link Vendors from linked Model 
            doRS.ClearLinkAll("LNK_RELATED_VE");         // Clear any existing connected Vendors
                                                         // CS 6/2/09: If var set, pull vendors from it
            if (goP.GetVar("QuoteLineInfo_" + doRS.GetFieldVal("GID_ID")) == "")
            {
                // doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_VE", 2), 2)
                if (doRS.GetFieldVal("LNK_FOR_MO%%CHK_WIP").ToString().ToUpper() == "CHECKED")
                    doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_VE", 2), 2);
                else
                    doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_VE", 2), 2);
            }
            else
            {
                doRS.SetFieldVal("LNK_RELATED_VE", goTR.StrRead(Convert.ToString(goP.GetVar("QuoteLineInfo_")) + Convert.ToString(doRS.GetFieldVal("GID_ID")), "MO_LNK_RELATED_VE", null, false));

            }

            // CS 8/8/11 commented
            // If goP.GetVar("QuoteInfo_" & doRS.GetFieldVal("LNK_IN_QT")) = "" Then
            // doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_TO_CO%%LNK_RELATED_VE", 2), 2)
            // Else
            // doRS.SetFieldVal("LNK_RELATED_VE", goTR.StrRead(goP.GetVar("QuoteInfo_" & doRS.GetFieldVal("LNK_IN_QT")), "CO_LNK_RELATED_VE"))
            // End If
            par_doCallingObject = doRS;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }
            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FORLINE_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curCost = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINECOST", 2));
            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINECOMMISSIONPERC"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_PD%%GID_ID"));
            string scommiss = Convert.ToString(doForm.doRS.GetFieldVal("SR__LINECOMMISSIONPERC"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_MODELNAME"));
            string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));



            //if (curUnitPrice <= 0)
            //{
            //    goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
            //    doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
            //    par_doCallingObject = doForm;
            //    return false;
            //}

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,LNK_FOR_PD,SR__QTY,CUR_COST,CUR_PRICEUNIT,SR__COMMISSIONPERC,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);
            rsQL.SetFieldVal("LNK_FOR_PD", PD_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("SR__COMMISSIONPERC", scommiss);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
             rsQL.SetFieldVal("CUR_Cost", curCost);
            rsQL.SetFieldVal("TXT_MODEL", sModelText);
            rsQL.SetFieldVal("MMO_DETAILS", sModelDesc);


            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);
            //rsQL.SetFieldVal("TXT_MODEL", sModelText);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}
            Refresh_QuoteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_PD");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("SR__LINECOMMISSIONPERC", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
                doForm.doRS.SetFieldVal("CUR_LINECOST", 1, 2);
            }

        }

        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_RELATED_PD,LNK_FOR_MO,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }

            iLineCount = iLineCount + 1;

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);


            //Mobile Line Number
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    iLineCount = doOPLines.Count();
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;


            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doForm;
            }
            return true;

        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;


            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doForm;
            }

            return true;

        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_Draft.docx";
                }
                else if (sQTTemplate == "Dezurik Quote")
                {
                    return "cus_DeZurik_Quote_Draft.docx";
                }
                else if (sQTTemplate == "Fair Sales Quote")
                {
                    return "cus_Fair Sales Quote_Draft.docx";
                }

            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote.docx";
                }
                if (sQTTemplate == "Dezurik Quote")
                {
                    return "cus_DeZurik_Quote.docx";
                }
                if (sQTTemplate == "Fair Sales Quote")
                {
                    return "cus_Fair Sales Quote.docx";
                }

            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                       
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            
            Refresh_QuoteTotal(doForm.doRS);
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
           doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_QuoteTotal(clRowSet doQuote)
        {
            string sGidId = Convert.ToString(doQuote.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_IN_QT='" + sGidId + "' ", "LNK_IN_QT", "CUR_SUBTOTAL|SUM");

            double curTotalAmt = 0.0;

            if ((rsQL.GetFirst() == 1))
            {
                //double curGP = 0.0;
                // int iGMPER = 0;
                curTotalAmt = Convert.ToDouble(rsQL.GetFieldVal("CUR_SUBTOTAL|SUM", 2));
                //curGP = Convert.ToDouble(rsQL.GetFieldVal("CUR_GROSSPROFIT|SUM", 2));
                //iGMPER = Convert.ToInt32(rsQL.GetFieldVal("SI__GROSSMARGIN|AVG", 2));

                doQuote.SetFieldVal("CUR_SUBTOTAL", curTotalAmt);
                doQuote.SetFieldVal("CUR_TOTAL", curTotalAmt);

            }
            else
            {
                doQuote.SetFieldVal("CUR_SUBTOTAL", 0.0);
                doQuote.SetFieldVal("CUR_TOTAL", 0.0);
                // doQuote.SetFieldVal("SI__GROSSMARGIN", 0.0);
            }
        }
        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_FOR_PD", rsOL.GetFieldVal("LNK_RELATED_PD", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", true);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "' And LNK_FOR_MO%%BI__ID<1 ", "LNK_IN_QT", "LNK_FOR_MO");

            if ((rsQL.GetFirst() == 1))
            {
                goErr.SetWarning(35000, sProc, "Please fill Model before saving the Quote  ");
                doForm.FieldInFocus = "LNK_FOR_MO";
                par_doCallingObject = doForm;
                return false;
            }

            return true;
        }


        public bool QT_FormControlOnChange_LNK_FORLINE_MO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            par_bRunNext = false;


            Form doForm = (Form)par_doCallingObject;




            decimal cSKUPrice = default(decimal);

            decimal cSKUCost;
            decimal rQtyFld;
            decimal cCostVal;

            clRowSet doRSModel;


            // If no model selected, return
            if (doForm.doRS.GetLinkCount("LNK_FORLINE_MO") == 0)
            {
                return true;

            }

            if (goData.IsFieldValid("MO", "TXT_Description"))
            {
                // Cs 6/22/09
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_FORLINE_MO") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_Description,CHK_WIP,txt_unittext,lnk_of_pd,cur_price,cur_cost");
            }

            else
            {
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_FORLINE_MO") + "'", null/* Conversion error: Set to default value for this argument */, "MMO_Description,CHK_WIP,txt_unittext,lnk_of_pd,cur_price,cur_cost");

            }

            if (doRSModel.GetFirst() != 1)
            {
                return true;

            }


            cSKUPrice = Convert.ToDecimal(doRSModel.GetFieldVal("CUR_PRICE", 2));
            doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", cSKUPrice, 2);

            cSKUCost = Convert.ToDecimal(doRSModel.GetFieldVal("CUR_COST", 2));
            if (cSKUCost != 0)
            {

                rQtyFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("SR__LineQTY", 2));
                cCostVal = cSKUCost * rQtyFld;
                doForm.doRS.SetFieldVal("CUR_LINECOST", cSKUPrice, 2);
            }








            par_doCallingObject = doForm;

            return true;
        }

        public bool GenerateSysName_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //CS 8/11/08: Added to Company sys name
            //MI 10/23/07 Added 'UTC' to AT Name.
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            //Array aTemp4 = default(Array);
            string[] aTemp4;
            string sRel = null;
            string sTemp5 = "";
            int i = 0;
            string sFileName = doRS.GetFileName();
            string sResult = "";
            clRowSet doLink = default(clRowSet);
            DateTime dttsterttime;
            DateTime gotrdatetiem;
            string par_sDelim = "|";
            //TLD 8/15/2013 Added Try, converted to _Pre
            //We assume that sFileName is valid. If this is a problem, test it here and SetError.
            //try
            //{
            switch (Strings.UCase(sFileName))
            {
                case "MO"        // MODEL		TXT_ModelName
               :
                    {
                        if (!doRS.IsLoaded("TXT_ModelName"))
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_ModelName");
                        // TLD 5/21/2013
                        // 'MI 4/2/08 TXT_Description replaces MMO_Description as of 4/3/08. MMO_Description remains in existing DBs.
                        // If goData.IsFieldValid("MO", "TXT_Description") Then
                        // If Not doRS.IsLoaded("TXT_Description") Then
                        // goErr.SetError(35103, sProc, , sFileName & ".TXT_Description")
                        // '35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        // End If
                        // Else
                        // If Not doRS.IsLoaded("MMO_Description") Then
                        // goErr.SetError(35103, sProc, , sFileName & ".MMO_Description")
                        // '35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        // End If
                        // End If

                        sTemp = Convert.ToString(doRS.GetFieldVal("TXT_ModelName", 0, 80));
                        // MI 4/2/08 TXT_Description replaces MMO_Description as of 4/3/08. MMO_ field remains in existing DBs.
                        // If goData.IsFieldValid("MO", "TXT_Description") Then
                        // sTemp2 = doRS.GetFieldVal("TXT_ProductCatalogName", , 44)
                        // Else
                        // sTemp2 = doRS.GetFieldVal("MMO_Description", , 44)
                        // End If
                        if (sTemp == "")
                            sTemp = "?";
                        sResult = sTemp;

                        // Model Name (30 + 2)
                        // Description (remaining space + 1)
                        // If sTemp2 = "" Then
                        // sResult = sTemp
                        // Else
                        // sResult = sTemp & " " & sTemp2
                        // End If

                        par_bRunNext = false;
                        break;
                    }
                case "OP":
                    {
                        // ==> OPP NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+LNK_For_Company%%TXT_CompanyName+" "+...
                        // LNK_For_Product%%TXT_ProductName+" "+CUR_Value
                        // OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> CUR_ValueIndex (MLS_Status)  
                        // OPP-COMPANY-0						OPP-PRODUCT-0

                        par_bRunNext = false;
                        if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_CreditedTo_US");

                        }
                        if (!doRS.IsLoaded("LNK_For_CO"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_For_CO");

                        }
                        if (!doRS.IsLoaded("LNK_For_PD"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_For_PD");

                        }
                        if (!doRS.IsLoaded("DTT_Time"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".DTT_Time");

                        }
                        if (!doRS.IsLoaded("CUR_Value"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".CUR_Value");

                        }
                        if (!doRS.IsLoaded("MLS_Status"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".MLS_Status");

                        }


                        // LNK_CreditedTo_US%%TXT_Code
                        sTemp = Convert.ToString(doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, true, 1));
                        if (sTemp == "")
                            // No records linked
                            sTemp = "?";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", null, "TXT_Code", 1);
                            if (doLink.Count() > 0)
                                sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                            else
                                sTemp = "?";
                        }

                        // LNK_For_CO%%TXT_CompanyName
                        sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_For_CO", 1, 1, false, 1));
                        if (sTemp2 == "")
                            // No records linked
                            sTemp2 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", null, "TXT_CompanyName", 1);
                            if (doLink.Count() > 0)
                                sTemp2 = doLink.GetFieldVal("TXT_CompanyName", 1, 22).ToString();
                            else
                                sTemp2 = "";
                        }

                        // LNK_For_Product%%TXT_ProductName
                        sTemp3 = doRS.GetFieldVal("LNK_For_PD", 1, 1, false, 1).ToString();
                        if (sTemp3 == "")
                            // No records linked
                            sTemp3 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("PD", 3, "GID_ID='" + sTemp3 + "'", null, "TXT_ProductName", 1);
                            if (doLink.Count() > 0)
                                sTemp3 = doLink.GetFieldVal("TXT_ProductName", 1, 14).ToString();
                            else
                                sTemp3 = "";
                        }

                        // Company (23)   '25
                        // Date (15)      '11
                        // Credited To User (5)
                        // Product (15)   '17
                        // Value (13)
                        // Status (9)
                        // *** MI 10/4/07 Added LocalToUTC conversion
                        // sResult = sTemp2 & " " & _
                        // goTR.DateToString(doRS.GetFieldVal("DTE_Time", clC.SELL_SYSTEM), "YYYY-MM-DD") & " " & _
                        // sTemp & " " & _
                        // sTemp3 & " " & _
                        // goTR.Pad(doRS.GetFieldVal("CUR_Value"), 11, " ", "L")
                        int par_iValid = 0;
                        string par_sdlim = "";
                        DateTime sDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
                        sResult = sTemp2 + " " + Strings.Left(goTR.DateTimeToSysString(goTR.UTC_LocalToUTC(ref sDate), ref par_iValid, ref par_sdlim), 10) + " GMT " + sTemp + " " + sTemp3 + " " + goTR.Pad(doRS.GetFieldVal("CUR_Value").ToString(), 11, " ", "L");



                        sResult += " [" + doRS.GetFieldVal("MLS_STATUS", 1, 8) + "]";
                        break;
                    }
                case "QL":

                    //CS 5/17/10: Added an additional char of padding to line no to accomodate > 99 quote lines sorting properly in the
                    //QL linkbox. Currently they sort like 1,2,..10,100,101,11. Also added a space to Model. Removed 2 spaces from Company.Cust Code.

                    if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_To_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_To_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("DTT_QteTime"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".DTT_QteTime");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_Model"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_Model");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("CUR_SubTotal"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".CUR_SubTotal");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //CS 6/2/09: Get var stored in QT_RecOnSave
                    string sQuoteInfo = goP.GetVar("QuoteInfo").ToString();

                    sTemp3 = doRS.GetFieldVal("LNK_In_QT", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp3))
                    {
                        //No records linked
                        sTemp3 = "?";
                    }
                    else
                    {
                        //CS 6/2/09: Check if we have this in project level var and use it.
                        if (string.IsNullOrEmpty(sQuoteInfo))
                        {
                            //Find the field value in the linked record
                            doLink = new clRowSet("QT", 3, "GID_ID='" + sTemp3 + "'", "", "DTT_Time", 1);
                            if (doLink.Count() > 0)
                            {
                                dttsterttime = Convert.ToDateTime(doLink.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
                                gotrdatetiem = Convert.ToDateTime(goTR.UTC_LocalToUTC(ref dttsterttime));
                                par_iValid = 4;
                                par_sDelim = " ";
                                //*** MI 10/4/07 Added LocalToUTC conversion
                                //sTemp3 = goTR.DateTimeToString(doLink.GetFieldVal("DTT_Time", clC.SELL_SYSTEM), clC.SELL_FORMAT_DATEDEF, "HH:mm", , " ")
                                sTemp3 = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(gotrdatetiem, ref par_iValid, ref par_sDelim), 16) + " GMT";
                            }
                            else
                            {
                                sTemp3 = "?";
                            }
                        }
                        else
                        {
                            dttsterttime = Convert.ToDateTime(goTR.StrRead(sQuoteInfo, "QT_DTT_TIME", "", false));
                            gotrdatetiem = Convert.ToDateTime(goTR.UTC_LocalToUTC(ref dttsterttime));
                            par_iValid = 4;
                            par_sDelim = " ";
                            sTemp3 = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(gotrdatetiem, ref par_iValid, ref par_sDelim), 16) + " GMT";
                        }
                    }

                    sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp))
                    {
                        //No records linked
                        sTemp = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                        }
                        else
                        {
                            sTemp = "?";
                        }
                    }

                    //CS 6/2/09: pull from var if set
                    if (string.IsNullOrEmpty(sQuoteInfo))
                    {
                        sTemp2 = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
                        if (string.IsNullOrEmpty(sTemp2))
                        {
                            //No records linked
                            sTemp2 = "?";
                        }
                        else
                        {
                            //Find the field value in the linked record
                            doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CustCode", 1);
                            if (doLink.Count() > 0)
                            {
                                sTemp2 = doLink.GetFieldVal("TXT_CustCode").ToString();
                            }
                            else
                            {
                                sTemp2 = "?";
                            }
                        }
                    }
                    else
                    {
                        sTemp2 = goTR.StrRead(sQuoteInfo, "CO_TXT_CUSTCODE", "", false);
                        //8/1/13 changed lang param
                    }

                    string TempLineNo = goTR.Pad(doRS.GetFieldVal("SR__LineNo", clC.SELL_FRIENDLY).ToString(), 6, " ", "L", true, "R");

                    //if (Convert.ToDecimal(TempLineNo) < 10)
                    //{
                    //    TempLineNo = " " + TempLineNo;
                    //}

                    sResult = "#" + TempLineNo + " " + goTR.Pad(sTemp3, 23, "", "R", true) + " " + goTR.Pad(sTemp, 4, " ", "R", true) + " " + goTR.Pad(sTemp2, 8, " ", "R", true) + " " + goTR.Pad(doRS.GetFieldVal("TXT_Model").ToString(), 11, " ", "R", true) + " " + goTR.Pad(doRS.GetFieldVal("SR__Qty").ToString(), 4, " ", "L", true, "R") + " " + goTR.Pad(doRS.GetFieldVal("TXT_Unit").ToString(), 5, " ", "R", true) + " " + goTR.Pad(doRS.GetFieldVal("CUR_SubTotal").ToString(), 11, " ", "L", true, "R");
                    par_bRunNext = false;
                    break;
            }


            sResult = goTR.Replace(sResult, Environment.NewLine, " ");
            sResult = goTR.Replace(sResult, Microsoft.VisualBasic.Strings.Chr(10).ToString(), " ");
            sResult = goTR.Replace(sResult, Microsoft.VisualBasic.Strings.Chr(13).ToString(), " ");
            sResult = goTR.Replace(sResult, Microsoft.VisualBasic.Constants.vbTab, " ");

            //1/28/15 Manmeet added replace for |
            sResult = goTR.Replace(sResult, "|", " ");

            //S_B Tkt#2407 Removing Html tags in Sys_Name
            sResult = sResult.Replace("<p>", "").Replace("<p/>", "");

            par_oReturn = sResult;

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}

            return true;
        }
        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }
    }
}
