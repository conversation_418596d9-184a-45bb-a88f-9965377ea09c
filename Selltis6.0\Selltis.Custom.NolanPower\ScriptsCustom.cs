﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

        }
        public ScriptsCustom()
        {
            Initialize();
        }


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.GetLinkCount("LNK_MergeTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (goP.GetVar("CN_Merge") != "1")
                    {
                        goP.SetVar("CN_Merge", "1");
                        doForm.MessageBox("This contact will be merged to the target contact, '" + doForm.doRS.GetFieldVal("LNK_MergeTo_CN%%SYS_Name") + "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target company. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", null/* Conversion error: Set to default value for this argument */, "MergeCN");
                        return true;
                    }
                }
            }

            // doForm.doRS.SetFieldVal("lnk_modifiedby_us", goP.GetMe("gid_id"))
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sUserID = goP.GetMe("ID");
            clRowSet doUSRS;

            // TLD 3/4/2010 Enable Team Leader linkbox based on user permission
            doUSRS = new clRowSet("US", 3, "GID_ID='" + sUserID + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID, SYS_NAME, CHK_COTEAMLEADEREDIT", 1);
            if (doUSRS.GetFirst() == 1)
            {
                if (Convert.ToInt32(doUSRS.GetFieldVal("CHK_COTEAMLEADEREDIT", 2)) == 1)
                // Enable checkbox
                {
                    doForm.SetControlState("LNK_TEAMLEADER_US", 0);
                }
                else
                {
                    doForm.SetControlState("LNK_TEAMLEADER_US", 4);
                }
            }

            // TLD 4/11/2011 Disable Customer Code
            doForm.SetControlState("TXT_CustCode", 4); // Disable for entry
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // doForm.doRS.SetFieldVal("lnk_modifiedby_us", goP.GetMe("gid_id"))

            if (doForm.doRS.GetLinkCount("LNK_MergeTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (goP.GetVar("CO_Merge") != "1")
                    {
                        goP.SetVar("CO_Merge", "1");
                        doForm.MessageBox("This company will be merged to the target company, '" + doForm.doRS.GetFieldVal("LNK_MergeTo_CO%%SYS_Name") + "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", null/* Conversion error: Set to default value for this argument */, "MergeCO");
                        return true;
                    }
                }
            }
            par_doCallingObject = doForm;

            return true;
        }

        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 4/11/2011 They disbled this field on open,
            // so since mandatory, enable if blank on save so they can
            // manually enter
            // Disable run in main
            goTR.StrWrite(ref par_sSections, "FillCustomerCode", "0");
            // If goScr.IsSectionEnabled(sProc, par_sSections, "FillCustomerCode") Then
            if (doForm.doRS.GetFieldVal("TXT_CUSTCODE") == "")
                scriptManager.RunScript("Company_FillCustCode",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null);// runs CheckDup
            if (Strings.Trim(Convert.ToString(doForm.doRS.GetFieldVal("TXT_CUSTCODE"))) == "")
            {
                doForm.MoveToField("TXT_CUSTCODE");
                doForm.SetControlState("TXT_CustCode", 0); // Enable for entry
                goErr.SetWarning(30200, sProc, "", "The Customer Code could not be generated automatically. Please enter it manually.", "", "", "", "", "", "", "", "", "TXT_CUSTCODE");
                return false;
            }
            // End If
            par_doCallingObject = doForm;
            return true;
        }

        public bool FIND_FormControlOnChange_BTN_QTSearch_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            // CS 8/17/09: Do NOT consider already saved filter of the desktop.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 7/14/2011 Prevent main from running
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            // Find dialog; Quote tab Search button
            string sQuoteNo;
            string sQuoteRefNo;
            string sCompany;
            string sCreditedTo;
            // TLD 7/14/2011 Added PO #
            string sPONO;
            // CS 10/17/2014
            string sDescr;

            string sView;
            int iCondCount = 0;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount = 0;
            int i;
            string sFilter = "";

            // Get values from form
            sQuoteNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_QUOTENO"));
            sQuoteRefNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_QUOTEREFNO"));
            sCompany = oForm.GetControlVal("NDB_TXT_COMPANY");
            sCreditedTo = oForm.GetControlVal("NDB_TXT_CREDITEDTO");
            // TLD 7/14/2011 Added PO #
            sPONO = oForm.GetControlVal("NDB_TXT_PO");
            // CS 10/17/2014: Added ability to search by Quote description
            sDescr = Strings.Trim(oForm.GetControlVal("NDB_TXT_DESCR"));
            if (sDescr != "")
            {
                sDescr = goTR.ConvertStringForQS(goTR.PrepareForSQL(sDescr), "TXT_DESCRIPTION","", true);
            }

            // Use values to filter Quote - Search Results desktop if it exists
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_D8E2511C-031F-45DD-5858-9AF500ECEC44");
            // Edit views in DT

            // View 1:Quote - Search Results
            sView = oDesktop.GetViewMetadata("VIE_4D836010-021D-4AD9-5858-9AF500ECEC44");
            // iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))


            // 'If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            // If iCondCount = 1 Then
            // If goTR.StrRead(sView, "C1FIELDNAME") = "<%ALL%>" Then
            // iCondCount = 0 'Will overwrite these values
            // End If
            // End If
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sQuoteNo != "")
                iCondCount = iCondCount + 1;
            if (sQuoteRefNo != "")
                iCondCount = iCondCount + 1;
            if (sCompany != "")
                iCondCount = iCondCount + 1;
            if (sCreditedTo != "")
                iCondCount = iCondCount + 1;
            // TLD 7/14/2011 Added PO #
            if (sPONO != "")
                iCondCount = iCondCount + 1;
            // CS 10/17/2014 Added Description
            if (sDescr != "")
                iCondCount = iCondCount + 1;

            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sQuoteNo != "")
            {
                // Add 'Quote No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_QUOTENO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sQuoteNo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_QUOTENO['" + sQuoteNo + "'";
                else
                    sFilter = "TXT_QUOTENO['" + sQuoteNo + "'";
            }
            if (sQuoteRefNo != "")
            {
                // Add 'Quote Ref No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_QUOTEREFNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sQuoteRefNo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_QUOTEREFNO['" + sQuoteRefNo + "'";
                else
                    sFilter = "TXT_QUOTEREFNO['" + sQuoteRefNo + "'";
            }
            if (sCompany != "")
            {
                // Add 'To Company' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_TO_CO%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCompany);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_TO_CO%%SYS_NAME['" + sCompany + "'";
                else
                    sFilter = "LNK_TO_CO%%SYS_NAME['" + sCompany + "'";
            }
            if (sCreditedTo != "")
            {
                // Add 'Credited To User' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CREDITEDTO_US%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCreditedTo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_CREDITEDTO_US%%SYS_NAME['" + sCreditedTo + "'";
                else
                    sFilter = "LNK_CREDITEDTO_US%%SYS_NAME['" + sCreditedTo + "'";
            }
            // TLD 7/14/2011 Added for PO #
            if (sPONO != "")
            {
                // Add 'Quote No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_PONO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sPONO);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_PONO['" + sPONO + "'";
                else
                    sFilter = "TXT_PONO['" + sPONO + "'";
            }
            // CS 10/17/2014 Added description
            if (sDescr != "")
            {
                // Add 'Quote description' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_DESCRIPTION%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sDescr);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_DESCRIPTION['" + sDescr + "'";
                else
                    sFilter = "TXT_DESCRIPTION['" + sDescr + "'";
            }


            // Edit CONDITION= line in view MD
            // sViewCondition = goTR.StrRead(sView, "CONDITION")
            // If sViewCondition = "" Then
            sNewCondition = sFilter; // No filter in view already
                                     // Else
                                     // sNewCondition = sViewCondition & " AND " & sFilter
                                     // End If
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_4D836010-021D-4AD9-5858-9AF500ECEC44", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que OP Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));

            goUI.Queue("", "");
            par_doCallingObject = oForm;
            return true;
        }

        public bool MergeCO(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            clRowSet rsTarget;

            try
            {

                // The rowset of the company being merged is doForm.doRS

                // The rowset of the company being merged to is:
                rsTarget = new clRowSet("CO", 1, "GID_ID = '" + doForm.doRS.GetFieldVal("LNK_MergeTo_CO") + "'", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true, true);
                clRowSet _ds = doForm.doRS; 
                // Only merging TXT, MMO (not merging dates, MLS, ?)
                MergeField(ref _ds, ref rsTarget, "MMO_COMPETINGPRODS", "MMO");
                MergeField(ref _ds, ref rsTarget, "MMO_DIRECTIONS", "MMO");
                MergeField(ref _ds, ref rsTarget, "MMO_NOTE", "MMO");
                MergeField(ref _ds, ref rsTarget, "MMO_PROFILE", "MMO");

                MergeField(ref _ds, ref rsTarget, "TXT_ADDRBILLING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ADDRSHIPPING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ADDRMAILING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_CITYBILLING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_CITYMAILING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_CITYSHIPPING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_COUNTRYBILLING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_COUNTRYMAILING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_COUNTRYSHIPPING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_CUSTCODE", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_CUSTNO", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_DUNSNO", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_SICCODE", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_STATEBILLING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_STATEMAILING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_STATESHIPPING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ZIPBILLING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ZIPMAILING", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ZIPSHIPPING", "TXT");

                MergeField(ref _ds, ref rsTarget, "TEL_PHONENO", "TXT");
                MergeField(ref _ds, ref rsTarget, "TEL_PHONE2", "TXT");
                MergeField(ref _ds, ref rsTarget, "TEL_FAXNO", "TXT");
                MergeField(ref _ds, ref rsTarget, "URL_WEBPAGE", "TXT");

                MergeField(ref _ds, ref rsTarget, "MLS_TYPE", "MLS");

                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_CO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_CO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_PARENT_CO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_SUBSIDIARY_CO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_AC", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_AP", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVEDIN_AC", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_CN", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_REFERREDBY_CN", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_REFERRED_CN", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_REFERREDBY_CO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_REFERRED_CO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVES_CN", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_MS", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_OP", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVEDIN_OP", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_PR", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVEDIN_PR", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVEDIN_QT", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RECEIVED_QT", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_QL", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_TD", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVEDIN_TD", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_HAS_TR", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_IN_TE", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_PURCHASESAT_LO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_DV", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_RL", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_GR", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_IU", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_SO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_TEAMLEADER_US", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVES_US", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_VE", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_USES_VE", "LNK");

                rsTarget.Commit();
                doForm.doRS.SetFieldVal("CHK_Merged", 1, 2);
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;

        }

        public bool MergeCN(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            clRowSet rsTarget;

            try
            {

                // The rowset of the contact being merged is doForm.doRS

                // The rowset of the contact being merged to is:
                rsTarget = new clRowSet("CN", 1, "GID_ID = '" + doForm.doRS.GetFieldVal("LNK_MergeTo_CN") + "'", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true, true);
                clRowSet _ds = doForm.doRS;
                // Only merging TXT, MMO (not merging dates, MLS, ?)
                MergeField(ref _ds, ref rsTarget, "MMO_NOTE", "MMO");

                MergeField(ref _ds, ref rsTarget, "TXT_ADDRBUSINESS", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ADDRHOME", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ADDROTHER", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_CITYBUSINESS", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_CITYOTHER", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_CITYHOME", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_COUNTRYBUSINESS", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_COUNTRYHOME", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_COUNTRYOTHER", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ASSISTANTNAME", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_LASTCONTACTEDBY", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ASSISTANTTITLE", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_STATEBUSINESS", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_STATEHOME", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_STATEOTHER", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_TITLETEXT", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ZIPBUSINESS", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_ZIPOTHER", "TXT");

                MergeField(ref _ds, ref rsTarget, "", "");

                // MergeField(doForm.doRS, rsTarget, "TXT_IMPSourceID", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPUnivID", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPP21ID", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPCRMID", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPLeadID", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPACTID", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPDFID", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPTCID", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPSPID", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPAcctNo", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPCompetitor", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPCustomer", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPPrincipal", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPProspect", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPRep", "TXT")
                // MergeField(doForm.doRS, rsTarget, "TXT_IMPSupplier", "TXT")

                MergeField(ref _ds, ref rsTarget, "TEL_BUSPHONE", "TXT");
                MergeField(ref _ds, ref rsTarget, "TEL_CELLPHONE", "TXT");
                MergeField(ref _ds, ref rsTarget, "TEL_FAX", "TXT");
                MergeField(ref _ds, ref rsTarget, "TEL_HOMEPHONE", "TXT");
                MergeField(ref _ds, ref rsTarget, "TEL_MAINPHONE", "TXT");
                MergeField(ref _ds, ref rsTarget, "TEL_OTHERPHONE", "TXT");
                MergeField(ref _ds, ref rsTarget, "TEL_PAGER", "TXT");
                MergeField(ref _ds, ref rsTarget, "TEL_ASSISTANTPHONE", "TXT");

                MergeField(ref _ds, ref rsTarget, "URL_WEB", "TXT");
                MergeField(ref _ds, ref rsTarget, "EML_EMAIL", "TXT");
                MergeField(ref _ds, ref rsTarget, "TXT_CONTACTCODE", "TXT");

                MergeField(ref _ds, ref rsTarget, "MLS_TYPE", "MLS");
                MergeField(ref _ds, ref rsTarget, "MLS_SALUTATION", "MLS");
                MergeField(ref _ds, ref rsTarget, "MLS_INFLUENCEROLE", "MLS");
                MergeField(ref _ds, ref rsTarget, "MLS_BUYINGROLE", "MLS");
                MergeField(ref _ds, ref rsTarget, "MLS_BUYINGSTYLE", "MLS");

                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_AC", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CCIN_AC", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVEDIN_AC", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_EX", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_EL", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_Attends_AP", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_ORIGINATED_OP", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_KEYSPONSORFOR_OP", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVEDIN_OP", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_ORIGINATED_PR", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVEDIN_PR", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_ORIGINATED_QT", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CCIN_QT", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_TD", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INVOLVEDIN_TD", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_CONNECTED_MS", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_GR", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_IU", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_SO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_RELATED_US", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_REFERREDBY_CN", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_REFERREDBY_CO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_REFERRED_CN", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_REFERRED_CO", "LNK");
                MergeField(ref _ds, ref rsTarget, "LNK_INTERESTEDIN_PD", "LNK");

                rsTarget.Commit();
                doForm.doRS.SetFieldVal("CHK_Merged", 1, 2);
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool MergeField(ref clRowSet rsMerged, ref clRowSet rsTarget, string sField, string sType)
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clArray oArray = new clArray();

            sType = Strings.UCase(sType);
            sField = Strings.UCase(sField);

            try
            {
                switch (sType)
                {
                    case "MMO":
                        {
                            if (rsTarget.GetFieldVal(sField) == "")
                            {
                                rsTarget.SetFieldVal(sField, rsMerged.GetFieldVal(sField));
                            }
                            else
                            {
                                rsTarget.SetFieldVal(sField, rsTarget.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + rsMerged.GetFieldVal(sField));
                            }
                            break;
                        }

                    case "TXT":
                    case "DTE":
                        {
                            if (rsTarget.GetFieldVal(sField) == "")
                            {
                                rsTarget.SetFieldVal(sField, rsMerged.GetFieldVal(sField));
                            }
                            break;
                        }

                    case "MLS":
                        {
                            if (Convert.ToInt32(rsTarget.GetFieldVal(sField, 2)) == 0)
                            {
                                rsTarget.SetFieldVal(sField, rsMerged.GetFieldVal(sField));
                            }
                            break;
                        }

                    case "LI_":
                        {
                            if (Convert.ToInt32(rsTarget.GetFieldVal(sField)) == 0)
                            {
                                rsTarget.SetFieldVal(sField, rsMerged.GetFieldVal(sField));
                            }
                            break;
                        }

                    case "LNK":
                        {
                            oTable = null;
                            oArray = rsMerged.GetLinkVal(sField,ref oArray,true,0,-1,"",ref oTable);
                            if (sField == "LNK_TEAMLEADER_US")
                            {
                                if (rsTarget.GetFieldVal("LNK_TEAMLEADER_US") == "")
                                {
                                    rsTarget.SetLinkVal(sField, oArray);
                                }
                            }
                            else
                            {
                                rsTarget.SetLinkVal(sField, oArray);
                            }
                            break;
                        }

                    default:
                        {
                            break;
                        }
                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);

                }
            }

            return true;
        }

        public bool MessageBoxEvent_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/27/07 Changed Dim doForm As clForm to Dim doForm As Object.
            // MI 11/21/07 Updated comments.
            // MI 10/8/07 Added Return True per CS.
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // par_doCallingObject is always the doForm under which MessageBox was executed.
            // par_doArray is an array of strings through which you can pass multiple strings to this method.
            // Par_s1 is the identifier of the button clicked. usually "YES", "NO", "CANCEL", or "1", "2", or "3". It is
            // one of the values from par_s1-3 passed to doForm.MessageBox itself, depending on the button clicked.
            // For example, if you click button 2, the value from par_s2 will be passed here. 
            // par_s2 is the value the user entered in an input box, if the type is input box. Else, blank.
            // par_s3 is the identifier of the third button clicked. usually "CANCEL" or "3".
            // Par_s4 can be whatever you want to pass to this method.
            // Par_s5 is the name of the script that called doform.MessageBox plus a description of what it's doing, e.g. "CO_FormOnSave_LinkTeamLeaderMsgBox".

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // DEBUG - MI experimenting with declaring a variable to be clForm or clDesktop
            // Dim oType As System.Type = Nothing
            // oType = par_doCallingObject.GetType.ToString
            // Dim doForm As oType

            // TLD 4/24/2009 
            // When Complete All checked on Corr In Box - My
            // par_callingObject is clDesktop, so throws an error
            // so changed per Christy
            // Dim doForm As clForm = par_doCallingObject
            Form doForm = (Form)par_doCallingObject;

            // Dim sWork As String 'input value
            // Dim sWork2 As String
            // Dim sJournal As String 'original value in journal field
            // Dim doRS As clRowSet
            // Dim bUpdateFailed As Boolean
            bool bNoPerm = false;
            bool bError = false;
            bool bReqMissing = false;
            // Dim sView As String

            try
            {
                switch (Strings.UCase(par_s5))
                {
                    case "MERGECO":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        scriptManager.RunScript("MergeCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
                                        goP.SetVar("CO_Merge", "");
                                        break;
                                    }

                                case "NO":
                                    {
                                        goP.SetVar("CO_Merge", "");
                                        doForm.doRS.ClearLinkAll("LNK_MergeTo_CO");
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        goP.SetVar("CO_Merge", "");
                                        // abort the save
                                        doForm.CancelSave();
                                        break;
                                    }
                            }

                            break;
                        }

                    case "MERGECN":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        scriptManager.RunScript("MergeCN", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
                                        goP.SetVar("CN_Merge", "");
                                        break;
                                    }

                                case "NO":
                                    {
                                        goP.SetVar("CN_Merge", "");
                                        doForm.doRS.ClearLinkAll("LNK_MergeTo_CN");
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        goP.SetVar("CN_Merge", "");
                                        // abort the save
                                        doForm.CancelSave();
                                        break;
                                    }
                            }

                            break;
                        }

                    default:
                        {
                            break;
                        }
                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;
            doForm.SetControlState("CUR_COST", 0);
            par_doCallingObject = doForm;
            return true;
        }

        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)
            if (par_s1 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;

            }
            else
            {
                doForm = (Form)par_doCallingObject;

            }

            decimal cPriceUnit= default(decimal);
            double rQtyFld = default(double);
            double rDiscPerc = default(double);
            decimal cDiscAddAmt = default(decimal);
            decimal cCostVal = default(decimal);
            decimal cWork = default(decimal);
            decimal cSubtotal = default(decimal);
            double rSalesTaxPerc = default(double);
            decimal cSKUCost = default(decimal);
            string sSKUCost;

            // PURPOSE:
            // Calc Subtotal if Include is checked, otherwise enter 0 as subtotal
            // Field 'Price Unit No Disc' = Unit Price before discount
            // Field 'Price Unit' = Unit Price after discount
            // RETURNS:
            // True.

            // goP.TraceLine("", "", sProc)

            // CS Need to check if coming from RecOnSave b/c in that case we are working with a rowset and 
            // otherwise we are on a form.
            if (par_s1 != "doRS")
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_INCLUDE", 2)) != 1)
                {
                    // Set calculated fields to 0
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_COST", 0, 2);
                    doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_SALESTAX", 0, 2);
                }
                else
                {
                    cPriceUnit = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2));
                    rQtyFld = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                    rDiscPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__DISCPERCENT", 2));
                    cDiscAddAmt = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_DISCADDLAMT", 2));
                    // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                    // cCostVal = doForm.doRS.GetFieldVal("CUR_COST", 2)
                    sSKUCost = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST"));
                    if (sSKUCost != "")
                    {
                        cSKUCost = goTR.StringToCurr(sSKUCost,"",ref par_iValid,"");

                    }
                    else
                    {
                        cSKUCost = 0;

                    }
                    cCostVal = Convert.ToDecimal(doForm.doRS.GetFieldVal("Cur_Cost")) * Convert.ToDecimal(rQtyFld);
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)


                    // Copy Cost from SKU and multiply it by Qty if the user edited Qty or if Qty is 0
                    // (we set it to 0 above if it is blank)
                    // bUpdCostVal is set to 1 in ControlOnLeave script for 'For Model'.

                    // Check if form variable has been set otherwise get an error comparing srQtyEnterVal as 
                    // a string ("") to a double (rQtyFld)
                    // Per MI, we can get rid of the form var dealing with changing the qty. We will always
                    // recalc regardless of it.
                    // If doForm.oVar.GetVar("srQtyEnterVal") <> "" Then
                    // If (rQtyFld <> doForm.oVar.GetVar("srQtyEnterVal")) Or (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // If doForm.oVar.Getvar("bUpdCostVal") <> "" Then
                    // If (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // CS: Moving this to QL_FormControlOnChange_USEMODELPRICE. When the user clicks this button the linked model's cost
                    // will be used.
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    // If sSKUCost <> "" Then 'CS added this If b/c if coming from QL_FormOnLoad the value
                    // 'is blank for cost and get error
                    // cSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")

                    // cCostVal = cSKUCost * rQtyFld
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
                    // End If
                    // End If

                    // Calculate unit price after discount
                    cWork = cPriceUnit - (cPriceUnit * Convert.ToDecimal(rDiscPerc) / 100);
                    doForm.doRS.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                    // Calculate Subtotal

                    cSubtotal = (cPriceUnit * Convert.ToDecimal(rQtyFld)) - (cPriceUnit * Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(rDiscPerc / 100)) + cDiscAddAmt;
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                    // Calc Gross Profit
                    // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                    // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS 6/13/07: Added rQtyField per DF
                    cWork = cSubtotal - cCostVal;
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                    // Sales tax
                    if (Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_TAXABLE", 2)))
                    {
                        // CS 6/2/09: Get value from variable if set
                        if (goP.GetVar("QuoteInfo") == "")
                        {
                            rSalesTaxPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));

                        }
                        else
                        {
                           
                            rSalesTaxPerc = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT","",true),"",ref par_iValid,""));

                        }


                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", cSubtotal * Convert.ToDecimal(rSalesTaxPerc) / 100);
                    }
                    else
                    {
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", 0);
                    }
                }
            }
            else if (Convert.ToInt32(doRS1.GetFieldVal("CHK_INCLUDE", 2)) != 1)
            {
                // Set calculated fields to 0
                doRS1.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                doRS1.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                doRS1.SetFieldVal("CUR_COST", 0, 2);
                doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                doRS1.SetFieldVal("CUR_SALESTAX", 0, 2);
            }
            else
            {
                cPriceUnit = Convert.ToDecimal(doRS1.GetFieldVal("CUR_PRICEUNIT", 2));
                rQtyFld = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                rDiscPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__DISCPERCENT", 2));
                cDiscAddAmt = Convert.ToDecimal(doRS1.GetFieldVal("CUR_DISCADDLAMT", 2));
                // cCostVal = doRS1.GetFieldVal("CUR_COST", 2)
                // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                string sModel = goP.GetVar("QuoteLineInfo_" + doRS1.GetFieldVal("GID_ID").ToString()).ToString();
                
                if (sModel == "")
                {
                    sSKUCost = Convert.ToString(doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST"));

                }
                else
                {
                    sSKUCost = goTR.StrRead(sModel, "MO_CUR_COST");

                }
                if (sSKUCost != "")
                {
                    cSKUCost = goTR.StringToCurr(sSKUCost,"",ref par_iValid,"");

                }
                else
                {
                    cSKUCost = 0;

                }
                cCostVal = Convert.ToDecimal(doRS1.GetFieldVal("CUR_COST")) * Convert.ToDecimal(rQtyFld);
                // doRS1.SetFieldVal("CUR_COST", cCostVal)

                // 'CS: only set the cost if it is 0
                // If cCostVal = 0 Then
                // cSKUCost = goTR.StringToCurr(doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST"))
                // cCostVal = cSKUCost * rQtyFld
                // doRS1.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal))
                // End If

                // Calculate unit price after discount
                cWork = cPriceUnit - (cPriceUnit * Convert.ToDecimal(rDiscPerc) / 100);
                doRS1.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                // Calculate Subtotal
                cSubtotal = (cPriceUnit * Convert.ToDecimal(rQtyFld) - (cPriceUnit * Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(rDiscPerc) / 100)) + cDiscAddAmt;
                doRS1.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                // Calc Gross Profit
                cWork = cSubtotal - cCostVal;
                // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS Added rQtyField 
                doRS1.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                // Sales tax
                if (Convert.ToBoolean(doRS1.GetFieldVal("CHK_TAXABLE", 2)))
                {
                    // CS 6/2/09:
                    if (Convert.ToString(goP.GetVar("QuoteInfo")) == "")
                    {
                        rSalesTaxPerc = Convert.ToDouble(doRS1.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                    }
                    else
                    {
                        rSalesTaxPerc = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT","",true), "", ref par_iValid, ""));
                    }
                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                    doRS1.SetFieldVal("CUR_SALESTAX", cSubtotal * Convert.ToDecimal(rSalesTaxPerc) / 100);
                }
                else
                {
                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0);
                    doRS1.SetFieldVal("CUR_SALESTAX", 0);
                }
            }
            if (par_s1 == "doRS")
            {
                par_doCallingObject = doRS1;

            }
            else
            {
                par_doCallingObject = doForm;

            }
            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/30/2010 Mod RS to optimize loaded fields
            // Dim doLines As New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO ASC", _
            // "", , , , , , , True)
            clRowSet doLines = new clRowSet("QL", 1, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "DTT_QTETIME D, SR__LINENO ASC", "*", 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true);

            if (doLines.GetFirst() == 1)
            {
                do
                {
                    doLines.SetFieldVal("LNK_CreditedTo_US", doForm.doRS.GetFieldVal("LNK_CreditedTo_US", 2), 2);
                    // doLines.SetFieldVal("LNK_Peer_US", doForm.doRS.GetFieldVal("LNK_Peer_US", 2), 2)
                    doLines.Commit();
                    if (doLines.GetNext() == 0)
                        break;
                }
                while (true);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here.
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }

        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script! 
            // par_doArray: Unused. 
            // par_s1: Unused. 
            // par_s2 to par_s5: Unused. 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process 
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running. 
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            clRowSet doRS = new clRowSet("TR", 3, "TXT_TermsName='" + goTR.PrepareForSQL("Net 30") + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID");
            if (doForm.GetMode() == "CREATION")
            {
                if (doRS.GetFirst() == 1)
                {
                    doForm.doRS.SetFieldVal("LNK_Related_TR", doRS.GetFieldVal("GID_ID"));
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

    }

}
