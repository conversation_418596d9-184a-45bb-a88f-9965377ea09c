﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Data.SqlClient;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";
        SqlConnection par_oConnection = null;

        public string sError;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
        }
        public ScriptsCustom()
        {
            Initialize();
        }

        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
            }


            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool DW_RunReport(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // PURPOSE:       This method runs the report creation and calculation process
            // CALLED FROM:   Timer Automator or DW designer page
            // PARAMS:        par_s1 = Metadata page ID (OTH_DW)

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // par_s1 = DW metadata page id

            try
            {
                string sLog = "";
                string sOutput = "";
                string sFile = "";

                // Get MD page and NAME, 
                string sDWPage = goMeta.PageRead("", par_s1); // par_s1 is OTH_DW PageID
                if (sDWPage == "")
                    goLog.Log(sProc, "Metadata page " + par_s1 + " does not exist", clC.SELL_LOGLEVEL_STANDARD, true);

                // Does this report write to a Selltis File?
                sOutput = goTR.StrRead(sDWPage, "CREATEFILE");
                if (sOutput == "1")
                {
                    // Does file exist
                    sFile = goTR.StrRead(sDWPage, "FILE");
                    if (sFile == "")
                    {
                        // Create Selltis File
                        sFile = DW_CreateSelltisfile(par_s1);
                        // write Selltis file to MD
                        goMeta.LineWrite("", par_s1, "FILE", sFile, ref par_oConnection);
                    }
                    if (Strings.UCase(par_s2) == "TRUE")
                        // Run calculations
                        DW_RunReportCalcs(par_s1);
                }
                else
                {
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public string DW_CreateSelltisfile(string sDWPageID)
        {

            // PURPOSE:   Create Selltis file, SQL table, and add fields from schema

            // PARAMS:    sDWPageID:  Metadata page ID (OTH_DW)


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PURPOSE: Get next unused file name in range 0A-9Z
            string sFileName = "";
            string sFieldName;
            string sLabel = "";
            string sPage = "";
            string sAlpha = "A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z";
            string[] aAlpha = Strings.Split(sAlpha, ",");
            DataTable dtSchema = new DataTable();

            try
            {

                // Reload project file data
                goData.LoadTableData();
                clArray aFiles = goData.GetFiles();

                for (int i = 0; i <= 9; i++)
                {
                    for (int j = 0; j <= aAlpha.GetUpperBound(0); j++)
                    {
                        sFileName = i.ToString() + aAlpha[j];
                        if (aFiles.Seek(sFileName) == 0)
                            // File does not exist, so it's the next one to use
                            goto FILEFOUND;
                    }
                }

                FILEFOUND:

                // Get file label
                sPage = goMeta.PageRead("", sDWPageID);
                sLabel = Strings.Left(goTR.StrRead(sPage, "NAME"), 30);

                if (sFileName == "")
                    return "";
                // sFileName is ok, so continue
                string bResult;

                // Get schema for this new file. From MD values
                dtSchema = DW_GetSchema(sDWPageID);

                // Create sql table and Selltis file with defaults
                if (DW_CreateTable(sFileName, sLabel, sLabel))
                {
                    // Alter table to append columns
                    for (int i = 0; i <= dtSchema.Rows.Count - 1; i++)
                    {
                        sFieldName = dtSchema.Rows[i]["sFieldName"].ToString();
                        bResult = DW_AddSqlField(sFileName, sFieldName).ToString();
                        goMeta.LineWrite("GLOBAL", "FLD_" + sFileName, "US_" + sFieldName, sFieldName, ref par_oConnection, "", "XX");
                    }
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return sFileName;
        }
        public bool DW_CreateTable(string sTableName, string sLabel, string sLabelPlural)
        {

            // PURPOSE:   Creates SQL table and Selltis file

            // PARAMS:    (Same as pCreateTable)

            // NOTE:      Calls pCreateTable
            // SQL table name auto-generated in DW_CreateSelltisFile (names in range 0A - 9Z)

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Dim iRet As Integer

            try
            {
                System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

                System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
                string sCommand = "";

                System.Data.SqlClient.SqlParameter par_sTableName = new System.Data.SqlClient.SqlParameter("@par_sTableName", SqlDbType.VarChar);
                par_sTableName.Value = sTableName;
                cmd.Parameters.Add(par_sTableName);

                System.Data.SqlClient.SqlParameter par_sTableLabel = new System.Data.SqlClient.SqlParameter("@par_sTableLabel", SqlDbType.VarChar);
                par_sTableLabel.Value = sLabel;
                cmd.Parameters.Add(par_sTableLabel);

                System.Data.SqlClient.SqlParameter par_sTableLabelPlural = new System.Data.SqlClient.SqlParameter("@par_sTableLabelPlural", SqlDbType.VarChar);
                par_sTableLabelPlural.Value = sLabelPlural;
                cmd.Parameters.Add(par_sTableLabelPlural);

                System.Data.SqlClient.SqlParameter par_bIsDataWarehouseFile = new System.Data.SqlClient.SqlParameter("@par_bIsDataWarehouseFile", SqlDbType.Int);
                par_bIsDataWarehouseFile.Value = 1;
                cmd.Parameters.Add(par_bIsDataWarehouseFile);

                System.Data.SqlClient.SqlParameter par_bCreateForm = new System.Data.SqlClient.SqlParameter("@par_bCreateForm", SqlDbType.Int);
                par_bCreateForm.Value = 0;
                cmd.Parameters.Add(par_bCreateForm);

                System.Data.SqlClient.SqlParameter par_bCreateDesktop = new System.Data.SqlClient.SqlParameter("@par_bCreateDesktop", SqlDbType.Int);
                par_bCreateDesktop.Value = 0;
                cmd.Parameters.Add(par_bCreateDesktop);

                // return parameter - not used
                System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", System.Data.SqlDbType.Int);
                retValParam.Direction = System.Data.ParameterDirection.ReturnValue;
                cmd.Parameters.Add(retValParam);

                try
                {
                    sCommand = "pCreateTable";
                    cmd.CommandText = sCommand;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Connection = sqlConnection1;
                    cmd.ExecuteNonQuery();
                    sqlConnection1.Close();
                }
                // Set the return value to what the sproc actually returns as a result
                // iRet = goTR.StringToNum(retValParam.Value, "0")
                // Return iRet

                catch (System.Data.SqlClient.SqlException ex)
                {
                    if (ex.Number == 2601)
                    {
                        // Duplicate record found error
                        sqlConnection1.Close();
                        //return -1;
                        return false;
                    }
                    else
                    {
                        // Another error
                        sqlConnection1.Close();
                        goErr.SetError(ex, 35000, sProc, "SQL error: " + ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public DataTable DW_GetSchema(string sDWPageID)
        {

            // PURPOSE:   Create schema datatable for new Selltis file/SQL table
            // Schema is derived from the OTH_DW_ metadata for column and sort properties

            // PARAMS:    sDWPageID:  Metadata page ID (OTH_DW)


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                string sPage = goMeta.PageRead("GLOBAL", sDWPageID);
                Hashtable oHashAxes = new Hashtable();
                string sDateInterval = "";
                string sStartDate = "";
                string sEndDate = "";
                DataTable dtSchema = new DataTable();
                string sFileName;
                // dtSchema = CreateSchemaTable()
                DataRow drSchema;

                // Get columns 1-4 from Y-sort
                int iRowIndex = 0;
                int iColIndex = 0;
                clArray aCol = new clArray();

                // Create Schema Table
                string sCol = "iCol,bSort,sFieldName,sSortType,sProperty";
                string[] aColSchema = Strings.Split(sCol, ",");
                string sPrefix;

                for (int i = 0; i <= aColSchema.GetUpperBound(0); i++)
                {
                    DataColumn dc = new DataColumn();
                    dc.ColumnName = aColSchema[i];
                    dtSchema.Columns.Add(dc);
                }

                // Create function to return the field type/prefix (CUR, INT, TXT) to be used in the calculated columns
                // Use TXT if KPIs are mix of CUR and INT, otherwise use CUR or INT
                sPrefix = DW_GetCalcColumnTypeFromKPIs(1, sDWPageID);

                for (int i = 1; i <= 4; i++)
                {
                    drSchema = dtSchema.NewRow();
                    // Dim oCheckbox As CheckBox = Master.FindControl("Main").FindControl("chkYSort" & i)
                    // If oCheckbox.Checked Then
                    if (goTR.StrRead(sPage, "SORT" + i) != "")
                    {
                        if (goTR.StrRead(sPage, "SORT" + i) == "1")
                        {
                            iColIndex += 1;
                            // Dim oRadioList As RadioButtonList = Master.FindControl("Main").FindControl("rbYSortType" & i)
                            // Dim sYSortType As String = oRadioList.SelectedValue
                            string sYSortType = goTR.StrRead(sPage, "SORT" + i + "TYPE");
                            switch (sYSortType)
                            {
                                case "Date Part":
                                    {
                                        aCol.Add("TXT_DateInt");    // Replace with schema table??
                                        sDateInterval = goTR.StrRead(sPage, "DATEINTERVAL");
                                        oHashAxes.Add("Y" + i.ToString(), "DATEINT=" + sDateInterval);
                                        dtSchema.Rows.Add(DW_CreateSchemaRow(ref drSchema, iColIndex, true, "TXT_DateInt", "DateInt", sDateInterval));
                                        break;
                                    }

                                case "Records from File":
                                    {
                                        sFileName = goTR.StrRead(sPage, "SORT" + i + "FILE");
                                        aCol.Add("TXT_" + sFileName);
                                        oHashAxes.Add("Y" + i.ToString(), "FILE=" + sFileName);
                                        dtSchema.Rows.Add(DW_CreateSchemaRow(ref drSchema, iColIndex, true, "TXT_" + sFileName, "File", "File|" + sFileName));
                                        break;
                                    }

                                case "Custom Value (KPI)":
                                    {
                                        aCol.Add("TXT_KPIName");
                                        oHashAxes.Add("Y" + i.ToString(), "KPI");
                                        dtSchema.Rows.Add(DW_CreateSchemaRow(ref drSchema, iColIndex, true, "TXT_KPIName", "KPI", ""));
                                        break;
                                    }

                                default:
                                    {
                                        break;
                                    }
                            }
                        }
                        else
                            break;
                    }
                }

                // Get add'l columns from X-sort
                var sXSortType = goTR.StrRead(sPage, "COLUMNTYPE");
                switch (sXSortType)
                {
                    case "Date Part":
                        {
                            iColIndex += 1;
                            sDateInterval = goTR.StrRead(sPage, "DATEINTERVAL");
                            sStartDate = goTR.StrRead(sPage, "STARTDATE");
                            sEndDate = goTR.StrRead(sPage, "ENDDATE");
                            DW_AddSchemaColumnsForDatePart(ref dtSchema, iColIndex, sStartDate, sEndDate, sDateInterval, sPrefix);
                            // sDateInterval = cmbXDatePart.SelectedValue
                            // sDateInterval = goTR.StrRead(sPage, "DATEINTERVAL")
                            oHashAxes.Add("X", "DATEINT=" + goTR.StrRead(sPage, "DATEINTERVAL"));
                            break;
                        }

                    case "Records from File":
                        {
                            iColIndex += 1;
                            sFileName = goTR.StrRead(sPage, "COLUMNFILE");
                            DW_AddSchemaColumnsForFile(ref dtSchema, iColIndex, sFileName, sPrefix);
                            oHashAxes.Add("X", "FILE=" + sFileName);
                            break;
                        }

                    case "Custom Value (KPI)":
                        {
                            iColIndex += 1;
                            DW_AddSchemaColumnsForKPIs(sPage, ref dtSchema, iColIndex, sPrefix);
                            break;
                        }

                    default:
                        {
                            break;
                        }
                }

                // dtSchema.TableName = sFileName
                // dtSchema.WriteXml("D:\Customer\GPS\SalesGPS\Downloads\" & sFileName & ".xml")

                return dtSchema;
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
                return null;
            }
        }
        public string DW_GetCalcColumnTypeFromKPIs(int iMode, string sPageID)
        {

            // PURPOSE:   Determine Selltis field type/prefix for calculated columns.
            // sPageID: MD page for the report

            // PARAMS:    sMode:  1 = ALL KPIs
            // 2 = Single KPI

            // RETURNS:   String. Either 'CUR_', 'INT_' or 'TXT_' if values are mixed

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sDWPage;
            int iKPICount;
            string sKPIPageID;
            string sFormula;
            string sPrefix = "";
            int iStartPos;
            int iEndPos;
            string sWork;

            try
            {

                // Get DW_ page
                sDWPage = goMeta.PageRead("", sPageID);
                par_iValid = 4;
                // Get KPI count if getting ALL KPIs
                if (iMode == 1)
                    iKPICount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sDWPage, "KPICOUNT"), "", ref par_iValid, ""));
                else
                    iKPICount = 1;
                // Loop: Get KPI page IDs
                for (int i = 1; i <= iKPICount; i++)
                {
                    // Get KPI page
                    sKPIPageID = goTR.StrRead(sDWPage, "KPI" + i);
                    // Get formula and field type
                    sFormula = goMeta.LineRead("", sKPIPageID, "FORMULA");
                    // Parse formula
                    iStartPos = Strings.InStr(sFormula, "%");
                    sWork = Strings.Right(sFormula, Strings.Len(sFormula) - iStartPos);
                    iEndPos = Strings.InStr(sWork, "_");
                    sWork = Strings.Left(sWork, iEndPos);
                    // Set/compare field type
                    if (sPrefix == "")
                        sPrefix = sWork;
                    else if (sPrefix != sWork)
                    {
                        sPrefix = "TXT_";
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
                return "TXT_";
            }

            // Return type
            return sPrefix;
        }
        public bool DW_AddSqlField(string sFile, string sFieldName)
        {

            // PURPOSE:   Add field to SQL table
            // Called after pCreateTable creates the table. Fields are added based on schema in GetSchema()

            // PARAMS:    sFile:      SQL table name
            // sFieldName: SQL field name


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Initialize()

            try
            {
                System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
                System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
                string sFieldType = "";
                int intLength = 0;
                string sFields = "";
                string sValues = "";
                int i = 0;
                string sSQL = "";

                // Get sFieldType from prefix (string or cur?)
                string sPrefix = Strings.Left(sFieldName, 3);
                switch (sPrefix)
                {
                    case "TXT":
                        {
                            sFieldType = "varchar";
                            intLength = 80;
                            break;
                        }

                    case "CUR":
                        {
                            sFieldType = "money";
                            break;
                        }
                }

                switch (sFieldType)
                {
                    case "datetime":
                        {
                            sFieldName = "DTT" + Strings.Mid(sFieldName, 4, Strings.Len(sFieldName) - 3);
                            break;
                        }

                    default:
                        {
                            break;
                        }
                }

                // Add column to sql table
                switch (sFieldType)
                {
                    case "varchar":
                    case "nvarchar":
                        {
                            sSQL = "ALTER TABLE [" + sFile + "] ADD " + sFieldName + " " + sFieldType + "(" + intLength + ") NULL";
                            break;
                        }

                    default:
                        {
                            sSQL = "ALTER TABLE [" + sFile + "] ADD " + sFieldName + " " + sFieldType;
                            break;
                        }
                }
                cmd.CommandText = sSQL;
                cmd.CommandType = System.Data.CommandType.Text;
                cmd.Connection = sqlConnection1;

                if (cmd.ExecuteNonQuery() != 0)
                {
                    sqlConnection1.Close();
                    return true;
                }
                else
                {
                    sqlConnection1.Close();
                    return false;
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public DataRow DW_CreateSchemaRow(ref DataRow dr, int iCol, bool bSort, string sFieldName, string sSortType, string sProperty)
        {

            // PURPOSE:   Adds a new dr row to the schema datatable

            // PARAMS:    iCol:       column index in the result table (row in the schema table)
            // bSort:      True if used as Y-sort (up to 4 columns)
            // sFieldName: name of the field/column to create
            // sSortType:  options are KPI, File, DateInt for the 3 sort types. Use 'Calc' for non-sorted columns
            // sProperty:  
            // sSortType = KPI:        KPI|[KPI Page ID]               KPI|OTH_KPI:00C2C1A5-09C6-4...
            // sSortType = File:       File|[FileName]                 File|US
            // sSortType = DateInt:    DateInt|[Interval]|[Interval #] DateInt|Month|01

            // RETURNS:   DataRow to be added to schema datatable

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                sProperty = Strings.Replace(sProperty, " ", "");
                string[] aProperty = Strings.Split(sProperty, ",");

                // "iCol,bSort,sFieldName,sSortType,sProperty"
                dr["iCol"] = iCol;
                dr["bSort"] = bSort;
                dr["sFieldName"] = sFieldName;
                dr["sSortType"] = sSortType;
                dr["sProperty"] = sProperty;
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return dr;
        }
        public bool DW_AddSchemaColumnsForDatePart(ref DataTable dtSchema, int iColIndex, string sStartDate, string sEndDate, string sInterval, string sPrefix)
        {

            // PURPOSE:   Adds a 'Result table' column definition to the schema table, for date intervals.
            // Each column in the result table is represented by a record (row) in the schema table

            // PARAMS:    dtSchema:   The schema table to which these rows are being added
            // iColIndex:  Column index in the result table (row in the schema table)
            // sStartDate: Start date of the entire date range which includes multiple date intervals
            // sEndDate:   End date of the entire date range which includes multiple date intervals
            // sInterval:  Date interval as
            // MONTH
            // QUARTER
            // sPrefix:    Field name prefix/type
            // CUR_, INT_, TXT_

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            int iIntervals;
            int iStartMonth = 0;
            int iStartYear = 0;
            int iStartQuarter = 0;
            int iEndMonth;
            int iEndQuarter;
            int iEndYear;
            int iMonths = 0;
            int iQuarters = 0;
            int iCurrentMonth;
            int iCurrentQuarter;
            int iCurrentYear;
            string sCurrentStartDate;
            string sCurrentEndDate;
            string sQuarterName = "";

            DataRow drSchema;
            string sFieldName;
            string sProperty = "";

            try
            {
                // Get start/end values
                if (goTR.IsDate(sStartDate))
                {
                    par_iValid = 4;
                    iStartMonth = Convert.ToInt32(goTR.StringToNum(goTR.GetMonth(DateTime.Parse(sStartDate)), "", ref par_iValid));
                    iStartYear = Convert.ToInt32(goTR.StringToNum(goTR.GetYear(DateTime.Parse(sStartDate)), "", ref par_iValid));
                    iStartQuarter = Convert.ToInt32(goTR.StringToNum(goTR.GetQuarter(DateTime.Parse(sStartDate)), "", ref par_iValid));
                    iEndMonth = Convert.ToInt32(goTR.StringToNum(goTR.GetMonth(DateTime.Parse(sEndDate)), "", ref par_iValid));
                    iEndYear = Convert.ToInt32(goTR.StringToNum(goTR.GetYear(DateTime.Parse(sEndDate)), "", ref par_iValid));
                    iEndQuarter = Convert.ToInt32(goTR.StringToNum(goTR.GetQuarter(DateTime.Parse(sEndDate)), "", ref par_iValid));
                    par_iValid = 3;
                    iMonths = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Month, goTR.StringToDate(sStartDate, "", ref par_iValid), goTR.StringToDate(sEndDate, "", ref par_iValid)) + 1);
                    iQuarters = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Quarter, goTR.StringToDate(sStartDate, "", ref par_iValid), goTR.StringToDate(sEndDate, "", ref par_iValid)) + 1);
                }
                else
                {
                }

                // How many intervals in the date range?
                switch (Strings.UCase(sInterval))
                {
                    case "MONTH":
                        {
                            iIntervals = iMonths;
                            iCurrentMonth = iStartMonth;
                            iCurrentYear = iStartYear;
                            for (int i = 1; i <= iIntervals; i++)
                            {
                                // Create schema row for each column. The fields are TXT_ to allow for INT and CUR formatting
                                // FieldName:
                                sFieldName = sPrefix;
                                // What is the 'name' of the interval? Month abbreviation plus 2 digit year
                                sFieldName += goTR.Pad(iCurrentYear.ToString(), 2, "0", "L");
                                sFieldName += goTR.Pad(iCurrentMonth.ToString(), 2, "0", "L");
                                sFieldName += DateAndTime.MonthName(iCurrentMonth, true);
                                // What is the current year?
                                sFieldName += Strings.Right(goTR.NumToString(iCurrentYear), 2);

                                sCurrentStartDate = iCurrentYear.ToString() + "-" + iCurrentMonth.ToString() + "-" + "01";
                                sCurrentEndDate = iCurrentYear.ToString() + "-" + iCurrentMonth.ToString() + "-" + DW_GetLastDayOfMonth(iCurrentMonth);

                                drSchema = dtSchema.NewRow();
                                dtSchema.Rows.Add(DW_CreateSchemaRow(ref drSchema, iColIndex, false, sFieldName, "Calc", "DATEINT|" + sInterval + "|" + sCurrentStartDate + "|" + sCurrentEndDate));

                                // Advance to next year if this is month 12
                                if (iCurrentMonth == 12)
                                {
                                    iCurrentYear += 1;
                                    iCurrentMonth = 1;
                                }
                                else
                                    iCurrentMonth += 1;
                                iColIndex += 1;
                            }

                            break;
                        }

                    case "QUARTER":
                        {
                            iIntervals = iQuarters;
                            iCurrentQuarter = iStartQuarter;
                            iCurrentYear = iStartYear;
                            for (int i = 1; i <= iIntervals; i++)
                            {
                                // Create schema row for each column. The fields are TXT_ to allow for INT and CUR formatting
                                // FieldName:
                                // sFieldName = "TXT_"
                                sFieldName = sPrefix;
                                // What is the 'name' of the interval? 'Q' plus 2 digit quarter plus 2 digit year
                                switch (iCurrentQuarter)
                                {
                                    case 1:
                                    case 2:
                                    case 3:
                                        {
                                            sQuarterName = "Q1";
                                            break;
                                        }

                                    case 4:
                                    case 5:
                                    case 6:
                                        {
                                            sQuarterName = "Q2";
                                            break;
                                        }

                                    case 7:
                                    case 8:
                                    case 9:
                                        {
                                            sQuarterName = "Q3";
                                            break;
                                        }

                                    case 10:
                                    case 11:
                                    case 12:
                                        {
                                            sQuarterName = "Q4";
                                            break;
                                        }
                                }
                                sFieldName += sQuarterName;
                                // What is the current year?
                                sFieldName += Strings.Right(goTR.NumToString(iCurrentYear), 2);

                                sCurrentStartDate = iCurrentYear.ToString() + "-" + DW_GetFirstMonthOfQuarter(iCurrentQuarter) + "-" + "01";
                                sCurrentEndDate = iCurrentYear.ToString() + "-" + DW_GetLastMonthOfQuarter(iCurrentQuarter) + "-" + DW_GetLastDayOfMonth(int.Parse(DW_GetLastMonthOfQuarter(iCurrentQuarter)));

                                drSchema = dtSchema.NewRow();
                                dtSchema.Rows.Add(DW_CreateSchemaRow(ref drSchema, iColIndex, false, sFieldName, "Calc", "DATEINT|" + sInterval + "|" + sCurrentStartDate + "|" + sCurrentEndDate));

                                // Advance to next year if this is month 12
                                if (iCurrentQuarter == 4)
                                {
                                    iCurrentQuarter = 1;
                                    iCurrentYear += 1;
                                }
                                else
                                    iCurrentQuarter += 1;
                                iColIndex += 1;
                            }

                            break;
                        }

                    case "YEAR":
                        {
                            break;
                        }
                }
            }

            // dtSchema.Rows.Add(DW_CreateSchemaRow(drSchema, iColIndex, False, "CUR_Jul", "Calc", "DATEINT|" & sInterval & "|07"))

            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool DW_AddSchemaColumnsForFile(ref DataTable dtSchema, int iColIndex, string sFileName, string sPrefix, string sCond = "")
        {

            // PURPOSE:   Adds a 'Result table' column definition to the schema table
            // Each column in the result table is represented by a record (row) in the schema table

            // PARAMS:    dtSchema:   The schema table to which these rows are being added
            // iColIndex:  Column index in the result table (row in the schema table)
            // sFileName:  2 char Selltis file name (US)
            // sCond:      Filter/Condition for rowset

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                string sSort;
                string sFields;
                string sFldName = "";
                DataRow drSchema;

                switch (Strings.UCase(sFileName))
                {
                    case "US":
                        {
                            sSort = "TXT_NameLast";
                            sFields = "TXT_NameFirst, TXT_NameLast";
                            break;
                        }

                    default:
                        {
                            sSort = "TXT_" + goData.GetFileLabel(sFileName) + "Name";
                            sFields = "TXT_" + goData.GetFileLabel(sFileName) + "Name";
                            break;
                        }
                }

                clRowSet oRS;
                oRS = new clRowSet(sFileName, 3, sCond, sSort, sFields);
                if (oRS.GetFirst()== 1)
                {
                    do
                    {
                        switch (Strings.UCase(sFileName))
                        {
                            case "US":
                                {
                                    sFldName = sPrefix + Convert.ToString(oRS.GetFieldVal("TXT_NameLast")) + Strings.Left(Convert.ToString(oRS.GetFieldVal("TXT_NameFirst")), 1);
                                    break;
                                }

                            default:
                                {
                                    sFldName = sPrefix + Convert.ToString(oRS.GetFieldVal(sFields));
                                    break;
                                }
                        }
                        // Strip non alphanumeric chars
                        sFldName = Strings.Replace(sFldName, " ", "");
                        string s = "";
                        for (int i = 1; i <= Strings.Len(sFldName); i++)
                        {
                            s = Strings.Mid(sFldName, i, 1);
                            if (s != "")
                            {
                                if (Strings.Asc(s) < 48)
                                    sFldName = Strings.Replace(sFldName, s, "");
                                if (Strings.Asc(s) > 57 & Strings.Asc(s) < 65)
                                    sFldName = Strings.Replace(sFldName, s, "");
                                if (Strings.Asc(s) > 90 & Strings.Asc(s) < 97 & Strings.Asc(s) != 95)
                                    sFldName = Strings.Replace(sFldName, s, "");
                                if (Strings.Asc(s) > 122)
                                    sFldName = Strings.Replace(sFldName, s, "");
                            }
                        }

                        drSchema = dtSchema.NewRow();
                        dtSchema.Rows.Add(DW_CreateSchemaRow(ref drSchema, iColIndex, false, sFldName, "Calc", "File|" + sFileName + "|" + Convert.ToString(oRS.GetFieldVal("GID_ID"))));
                        iColIndex += 1;
                        if (oRS.GetNext() == 0)
                            break;
                    }
                    while (true);// 95 is underscore
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool DW_AddSchemaColumnsForKPIs(string sPage, ref DataTable dtSchema, int iColIndex, string sPrefix)
        {

            // PURPOSE:   Adds a 'Result table' column definition to the schema table, for date intervals.
            // Each column in the result table is represented by a record (row) in the schema table

            // PARAMS:    sPage:      DW_ metadata page
            // dtSchema:   The schema table to which these rows are being added
            // iColIndex:  Column index in the result table (row in the schema table)
            // 

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                string sFldName = "";
                DataRow drSchema;
                
                par_iValid = 4;
                // get KPI's
                int iKPICount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sPage, "KPICOUNT"), "", ref par_iValid));
                for (int i = 1; i <= iKPICount; i++)
                {
                    drSchema = dtSchema.NewRow();
                    string sKPIID = goTR.StrRead(sPage, "KPI" + i);
                    string sColumnName = goMeta.LineRead("", sKPIID, "NAME");
                    sColumnName = Strings.Replace(sColumnName, " ", "");
                    sColumnName = sPrefix + sColumnName;
                    dtSchema.Rows.Add(DW_CreateSchemaRow(ref drSchema, iColIndex, false, sColumnName, "Calc", "KPI" + "|" + sKPIID));
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool DW_RunReportCalcs(string sDWPageID)
        {

            // PURPOSE:       Run KPI calculations for the report defined by OTH_DW page
            // PARAMS:        sDWPageID:  Metadata page ID (OTH_DW)
            // CALLED FROM:   DW_RunReport or from autormator or other script/page

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sPage = goMeta.PageRead("GLOBAL", sDWPageID);
            string sDWFileName = goTR.StrRead(sPage, "FILE");
            DataTable dtSchema = DW_GetSchema(sDWPageID);

            DataTable dtKPI = null;
            DataTable dtDateInt = null;
            DataTable dtFile1 = null;
            DataTable dtFile2 = null;
            DataTable dtFile3 = null;
            DataTable dtFile4 = null;

            int iKPICount = 0;
            int iDateIntCount = 0;
            int iFileCount1 = 0;
            int iFileCount2 = 0;
            int iFileCount3 = 0;
            int iFileCount4 = 0;
            clArray aSorts = new clArray();

            string sFileName;
            string sFilter = "";

            try
            {
                // Need to reinitialize files/tables/fields
                goData.LoadSchemaData();

                // Clear data in table
                clRowSet dRS;
                dRS = new clRowSet(sDWFileName, 1);
                if (dRS.GetFirst()== 1)
                    dRS.DeleteAll();

                // Iterate rows in schema table to CREATE the SORT TABLES used to populate the sort columns
                for (int i = 0; i <= dtSchema.Rows.Count - 1; i++)
                {
                    DataRow dr = dtSchema.Rows[i];
                    // Is schema record(i) a sort column?
                    if (Convert.ToBoolean(dr["bSort"]) == true)
                    {
                        // -- Set sort datatables --
                        string sSortType = dr["sSortType"].ToString();
                        aSorts.Add(Strings.UCase(sSortType));
                        switch (Strings.UCase(sSortType))
                        {
                            case "KPI":
                                {
                                    dtKPI = DW_GetKPITable(sDWPageID);
                                    iKPICount = dtKPI.Rows.Count;
                                    break;
                                }

                            case "DATEINT":
                                {
                                    dtDateInt = DW_GetDateIntTable(sDWPageID);
                                    iDateIntCount = dtDateInt.Rows.Count;
                                    break;
                                }

                            case "FILE":
                                {
                                    switch (i)
                                    {
                                        case 0:
                                            {
                                                dtFile1 = DW_GetFileTable(sDWPageID, i + 1);
                                                iFileCount1 = dtFile1.Rows.Count;
                                                break;
                                            }

                                        case 1:
                                            {
                                                dtFile2 = DW_GetFileTable(sDWPageID, i + 1);
                                                iFileCount2 = dtFile2.Rows.Count;
                                                break;
                                            }

                                        case 2:
                                            {
                                                dtFile3 = DW_GetFileTable(sDWPageID, i + 1);
                                                iFileCount3 = dtFile3.Rows.Count;
                                                break;
                                            }

                                        case 3:
                                            {
                                                dtFile4 = DW_GetFileTable(sDWPageID, i + 1);
                                                iFileCount4 = dtFile4.Rows.Count;
                                                break;
                                            }
                                    }

                                    break;
                                }
                        }
                    }
                    else
                    {
                    }
                }

                // Populate data rows
                DataTable dtResult = new DataTable();       // = CreateResultTable(dtSchema)     'Modified from test page
                DataRow drResult;
                int iSortFound = 0;
                int iSortCount1 = 0;
                int iSortCount2 = 0;
                int iSortCount3 = 0;
                int iSortCount4 = 0;
                string sSort1Val = "";
                string sSort2Val = "";
                string sSort3Val = "";
                string sSort4Val = "";
                int iHighestSortCount = 0;
                int iKPISortColumn = -1;

                // Establish COUNTS for all SORTS
                DataRow drSchema = dtSchema.Rows[0];
                if (Convert.ToBoolean(drSchema["bSort"]))
                {
                    iHighestSortCount = 1;
                    string sSortType = drSchema["sSortType"].ToString();
                    switch (Strings.UCase(sSortType))
                    {
                        case "KPI":
                            {
                                iKPISortColumn = 0;
                                iSortCount1 = iKPICount;
                                break;
                            }

                        case "DATEINT":
                            {
                                iSortCount1 = iDateIntCount;
                                break;
                            }

                        case "FILE":
                            {
                                iSortCount1 = iFileCount1;
                                break;
                            }
                    }
                }

                // 2nd sort
                drSchema = dtSchema.Rows[1];
                if (Convert.ToBoolean(drSchema["bSort"]))
                {
                    iHighestSortCount = 2;
                    string sSortType = drSchema["sSortType"].ToString();
                    switch (Strings.UCase(sSortType))
                    {
                        case "KPI":
                            {
                                iKPISortColumn = 1;
                                iSortCount2 = iKPICount;
                                break;
                            }

                        case "DATEINT":
                            {
                                iSortCount2 = iDateIntCount;
                                break;
                            }

                        case "FILE":
                            {
                                iSortCount2 = iFileCount2;
                                break;
                            }
                    }
                }

                // 3rd sort
                drSchema = dtSchema.Rows[2];
                if (Convert.ToBoolean(drSchema["bSort"]))
                {
                    iHighestSortCount = 3;
                    string sSortType = drSchema["sSortType"].ToString();
                    switch (Strings.UCase(sSortType))
                    {
                        case "KPI":
                            {
                                iKPISortColumn = 2;
                                iSortCount3 = iKPICount;
                                break;
                            }

                        case "DATEINT":
                            {
                                iSortCount3 = iDateIntCount;
                                break;
                            }

                        case "FILE":
                            {
                                iSortCount3 = iFileCount3;
                                break;
                            }
                    }
                }

                // 4th sort
                drSchema = dtSchema.Rows[3];
                if (Convert.ToBoolean(drSchema["bSort"]))
                {
                    iHighestSortCount = 4;
                    string sSortType = drSchema["sSortType"].ToString();
                    switch (Strings.UCase(sSortType))
                    {
                        case "KPI":
                            {
                                iKPISortColumn = 3;
                                iSortCount4 = iKPICount;
                                break;
                            }

                        case "DATEINT":
                            {
                                iSortCount4 = iDateIntCount;
                                break;
                            }

                        case "FILE":
                            {
                                iSortCount4 = iFileCount4;
                                break;
                            }
                    }
                }

                // Create dtResult columns
                for (int iColCount = 0; iColCount <= dtSchema.Rows.Count - 1; iColCount++)
                {
                    DataColumn dc = new DataColumn();
                    dc.ColumnName = dtSchema.Rows[iColCount]["sFieldName"].ToString();
                    dtResult.Columns.Add(dc);
                }

                // Populate SORT VALUES in result table columns
                // drResult = dtResult.NewRow
                if (iSortCount1 > 0)
                {
                    for (int i = 0; i <= iSortCount1 - 1; i++)
                    {
                        switch (aSorts.GetItem(1))   // (i + 1)
                        {
                            case "KPI":
                                {
                                    // drResult(0) = dtKPI.Rows(i).Item("ID")
                                    sSort1Val = dtKPI.Rows[i]["ID"].ToString();
                                    break;
                                }

                            case "DATEINT":
                                {
                                    // drResult(0) = dtDateInt.Rows(i).Item("ID")
                                    sSort1Val = dtDateInt.Rows[i]["ID"].ToString();
                                    break;
                                }

                            case "FILE":
                                {
                                    // drResult(0) = dtFile1.Rows(i).Item("GID_ID")
                                    sSort1Val = dtFile1.Rows[i]["GID_ID"].ToString();
                                    break;
                                }
                        }
                        if (iHighestSortCount == 1)
                        {
                            drResult = dtResult.NewRow();
                            drResult[0] = sSort1Val;
                            dtResult.Rows.Add(drResult);
                        }
                        if (iSortCount2 > 0)
                        {
                            for (int j = 0; j <= iSortCount2 - 1; j++)
                            {
                                switch (aSorts.GetItem(2))   // (j + 1)
                                {
                                    case "KPI":
                                        {
                                            sSort2Val = dtKPI.Rows[j]["ID"].ToString();
                                            break;
                                        }

                                    case "DATEINT":
                                        {
                                            sSort2Val = dtDateInt.Rows[j]["ID"].ToString();
                                            break;
                                        }

                                    case "FILE":
                                        {
                                            sSort2Val = dtFile2.Rows[j]["GID_ID"].ToString();
                                            break;
                                        }
                                }
                                if (iHighestSortCount == 2)
                                {
                                    drResult = dtResult.NewRow();
                                    drResult[0] = sSort1Val;
                                    drResult[1] = sSort2Val;
                                    dtResult.Rows.Add(drResult);
                                }
                                if (iSortCount3 > 0)
                                {
                                    for (int k = 0; k <= iSortCount3 - 1; k++)
                                    {
                                        // new dr here and then fill columns 0 - x
                                        switch (aSorts.GetItem(3))   // (k + 1)
                                        {
                                            case "KPI":
                                                {
                                                    sSort3Val = dtKPI.Rows[k]["ID"].ToString();
                                                    break;
                                                }

                                            case "DATEINT":
                                                {
                                                    sSort3Val = dtDateInt.Rows[k]["ID"].ToString();
                                                    break;
                                                }

                                            case "FILE":
                                                {
                                                    sSort3Val = dtFile3.Rows[k]["GID_ID"].ToString();
                                                    break;
                                                }
                                        }
                                        if (iHighestSortCount == 3)
                                        {
                                            drResult = dtResult.NewRow();
                                            drResult[0] = sSort1Val;
                                            drResult[1] = sSort2Val;
                                            drResult[2] = sSort3Val;
                                            dtResult.Rows.Add(drResult);
                                        }
                                        if (iSortCount4 > 0)
                                        {
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // CALCULATIONS start here - loop thru remaining columns in dtSchema
                // Identify columns by name - schema table records that start with 'Calc'
                // cells are the individual KPI calcs
                // what is row y filter?
                string sProperty = "";
                string[] aProperty;
                string sXFilter = "";
                string sYFilter = "";
                string sKPIFile = "";
                // filter on first sort which is first column in the row
                // what type of sort? File/Date/KPI
                // Maybe this:
                int iRowCount = 0;
                // For EACH ROW in RESULT table
                foreach (DataRow drrResult in dtResult.Rows)
                {
                    iRowCount += 1; // testing
                    for (int i = iHighestSortCount; i <= dtSchema.Rows.Count - 1; i++) // Calc Columns in dtResult (not incl sort columns)
                    {
                        drSchema = dtSchema.Rows[i];
                        if (Strings.UCase(drSchema["sSortType"].ToString()) == "CALC")
                        {
                            sYFilter = "";
                            for (int iFilterCount = 0; iFilterCount <= 3; iFilterCount++)
                            {
                                switch (Strings.UCase(dtSchema.Rows[iFilterCount]["sSortType"].ToString()))
                                {
                                    case "KPI":
                                        {
                                            break;
                                        }

                                    case "DATEINT":
                                        {
                                            // Determine date filter with startdate and enddate
                                            // dtdateint has translation between datepart and start/end date
                                            string sStartEnd = "";
                                            string sStartDate = "";
                                            string sEndDate = "";

                                            string sDatePart = drrResult[iFilterCount].ToString();
                                            DataView dvDateInt = dtDateInt.DefaultView;
                                            dvDateInt.RowFilter = "ID='" + sDatePart + "'";
                                            if (dvDateInt.Table.Rows.Count > 0)
                                            {
                                                sStartEnd = dvDateInt.Table.Rows[0]["Property"].ToString();
                                                sStartDate = Strings.Split(sStartEnd, "|")[0];
                                                sEndDate = Strings.Split(sStartEnd, "|")[1];
                                            }
                                            // What file for this KPI?
                                            if (iKPISortColumn == -1)
                                                sKPIFile = DW_GetKPIProperty(drrResult[iKPISortColumn].ToString(), "FILE");
                                            else
                                            {
                                                // iKPISortCol is -1 and therefore is x-sort (columns are KPI's)
                                                // So what is the columnname/KPI?  >Column names are NAME property of KPI minus invalid chars
                                                // Need list of all KPIs and compare NAME value, minus spaces
                                                string sKPIName;
                                                string sKPIPageID;
                                                string sResultTableColumnName;
                                                clRowSet kpiRS;

                                                sResultTableColumnName = dtResult.Columns[i].ColumnName;
                                                // remove prefix
                                                sResultTableColumnName = Strings.Right(sResultTableColumnName, Strings.Len(sResultTableColumnName) - 4);

                                                kpiRS = new clRowSet("MD", 3, "TXT_Page [='OTH_KPI'", null, "*", -1, "", "", "", "", "", false, false, false, false, -1, "", false, true);
                                                if (kpiRS.GetFirst()== 1)
                                                {
                                                    do
                                                    {
                                                        if (Convert.ToString(kpiRS.GetFieldVal("TXT_Property")) == "NAME")
                                                        {
                                                            sKPIName = kpiRS.GetFieldVal("TXT_Value").ToString();
                                                            sKPIName = Strings.Replace(sKPIName, " ", "");
                                                            if (sKPIName == sResultTableColumnName)
                                                            {
                                                                // found the KPI page
                                                                sKPIPageID = Convert.ToString(kpiRS.GetFieldVal("TXT_Page"));
                                                                sKPIFile = DW_GetKPIProperty(sKPIPageID, "FILE");
                                                                break;
                                                            }
                                                        }

                                                        if (kpiRS.GetNext()== 0)
                                                            break;
                                                    }
                                                    while (true);
                                                }
                                            }
                                            // What date field is used in OTH_GOALS for this file? File is AC, OP, QL, ...
                                            string sDateFieldFromGoals = DW_GetDateFieldFromGoals(sKPIFile);
                                            if (sStartDate != "" & sEndDate != "")
                                            {
                                                if (sYFilter == "")
                                                    sYFilter = sDateFieldFromGoals + ">='" + sStartDate + "' AND " + sDateFieldFromGoals + "<='" + sEndDate + "'";
                                                else
                                                    sYFilter += " AND " + sDateFieldFromGoals + ">='" + sStartDate + "' AND " + sDateFieldFromGoals + "<='" + sEndDate + "'";
                                            }

                                            break;
                                        }

                                    case "FILE":
                                        {
                                            // what is the file?
                                            sProperty = dtSchema.Rows[iFilterCount]["sProperty"].ToString();
                                            string sFile = Strings.Split(sProperty, "|")[1];
                                            // link name from oth_goals
                                            // what is calc's file? 
                                            // What file for this KPI?
                                            if (iKPISortColumn == -1)
                                                sKPIFile = DW_GetKPIProperty(drrResult[iKPISortColumn].ToString(), "FILE");
                                            else
                                            {
                                                // iKPISortCol is -1 and therefore is x-sort (columns are KPI's)
                                                // So what is the columnname/KPI?  >Column names are NAME property of KPI minus invalid chars
                                                // Need list of all KPIs and compare NAME value, minus spaces
                                                string sKPIName;
                                                string sKPIPageID;
                                                string sResultTableColumnName;
                                                clRowSet kpiRS;

                                                sResultTableColumnName = dtResult.Columns[i].ColumnName;
                                                // remove prefix
                                                sResultTableColumnName = Strings.Right(sResultTableColumnName, Strings.Len(sResultTableColumnName) - 4);

                                                kpiRS = new clRowSet("MD", 3, "TXT_Page [='OTH_KPI'", null, "*", -1, "", "", "", "", "", false, false, false, false, -1, "", false, true);
                                                if (kpiRS.GetFirst()== 1)
                                                {
                                                    do
                                                    {
                                                        if (Convert.ToString(kpiRS.GetFieldVal("TXT_Property")) == "NAME")
                                                        {
                                                            sKPIName = Convert.ToString(kpiRS.GetFieldVal("TXT_Value"));
                                                            sKPIName = Strings.Replace(sKPIName, " ", "");
                                                            if (sKPIName == sResultTableColumnName)
                                                            {
                                                                // found the KPI page
                                                                sKPIPageID = Convert.ToString(kpiRS.GetFieldVal("TXT_Page"));
                                                                sKPIFile = DW_GetKPIProperty(sKPIPageID, "FILE");
                                                                break;
                                                            }
                                                        }

                                                        if (kpiRS.GetNext()== 0)
                                                            break;
                                                    }
                                                    while (true);
                                                }
                                            }
                                            string sLink = DW_GetLinkNameFromGoals(sKPIFile, sFile);
                                            if (sYFilter == "")
                                                sYFilter = sLink + " = '" + drrResult[iFilterCount].ToString() + "'";
                                            else
                                                sYFilter += " AND " + sLink + " = '" + drrResult[iFilterCount].ToString() + "'";
                                            break;
                                        }

                                    case "CALC":
                                        {
                                            break;
                                        }
                                }
                            }
                            // column x filter - still going across the row in dtResult
                            sProperty = Convert.ToString(drSchema["sProperty"]);
                            aProperty = Strings.Split(sProperty, "|");
                            switch (Strings.UCase(aProperty[0].ToString()))
                            {
                                case "DATEINT":
                                    {
                                        string sStartDate;
                                        string sEndDate;
                                        string sDateField;

                                        sDateField = DW_GetDateFieldFromGoals(sKPIFile);
                                        sStartDate = aProperty[2];
                                        sEndDate = aProperty[3];
                                        sXFilter = sDateField + " >= '" + sStartDate + "' AND " + sDateField + " <= '" + sEndDate + "'";
                                        break;
                                    }

                                case "FILE":
                                    {
                                        // what is the file?
                                        sFileName = aProperty[1].ToString();
                                        // link name from oth_goals
                                        // what is calc's file? get kpi ID from column ?? iKPISortColumn
                                        // iKPISortColumn is col 0 in result table
                                        sKPIFile = DW_GetKPIProperty(drrResult[iKPISortColumn].ToString(), "FILE");
                                        string sLink = DW_GetLinkNameFromGoals(sKPIFile, sFileName);

                                        // sXFilter = "LNK_[link name from oth_goals]_" & sFileName = "[GID_ID of record on x axis column]"
                                        // What is [GID_ID of record on x axis column]
                                        sXFilter = sLink + " = '" + aProperty[2] + "'";
                                        break;
                                    }

                                case "KPI":
                                    {
                                        // KPI has no x filter
                                        sXFilter = "";
                                        break;
                                    }
                            }
                        }


                        // ------------------------- FORMULA/CALCULATION HERE -------------------------
                        // Get aggregate rowset(s) and final value for this row/col
                        // What is the kpi field being summed/calc'd?
                        string s = "";
                        string sKPIFormula = "";
                        // If KPI in y sort column
                        if (iKPISortColumn > -1)
                            sKPIFormula = DW_GetKPIProperty(drrResult[iKPISortColumn].ToString(), "FORMULA");
                        else
                        {
                            // If KPI in x columns, get KPI page ID from dtschema
                            sKPIFormula = DW_GetKPIProperty(Strings.Split(drSchema["sProperty"].ToString(), "|")[1], "FORMULA");
                            sKPIFile = DW_GetKPIProperty(Strings.Split(drSchema["sProperty"].ToString(), "|")[1], "FILE");
                        }

                        int iStartPos = Strings.InStr(sKPIFormula, "%") + 1;
                        s = Strings.Mid(sKPIFormula, iStartPos, Strings.Len(sKPIFormula) - iStartPos);
                        int iEndPos = Strings.InStr(s, "%") - 1;
                        s = Strings.Left(s, iEndPos);
                        string sCalcField = s;

                        sFilter = "";
                        if (sXFilter != "")
                            sFilter = sXFilter;
                        if (sFilter == "")
                            sFilter = sYFilter;
                        else if (sYFilter != "")
                            sFilter += " AND " + sYFilter;

                        clRowSet oRS;
                        oRS = new clRowSet(sKPIFile, clC.SELL_GROUPBY, sFilter, "DTY_Time", sCalcField + "|SUM");
                        // If sYFilter.Contains("a36bb9a4-ad7e-4d0c-5553-9da700fe8232") Then
                        // Debug.WriteLine("here")
                        // End If
                        // oRS = New clRowSet(sKPIFile, 3, sXFilter & " AND " & sYFilter, "DTY_Time", sCalcField)
                        string sVal = "";
                        if (oRS.GetFirst()== 1)
                            sVal = Convert.ToString(oRS.GetFieldVal(sCalcField + "|SUM"));
                        // ------------------------------------------------------------------------------

                        // Format result string if going into TXT_ field/column
                        // what is the field name prefix? Is it TXT_?
                        if (dtResult.Columns[i].ColumnName.Contains("TXT_"))
                        {
                            // What is the field type of the KPI?
                            string sPrefix = DW_GetCalcColumnTypeFromKPIs(2, drrResult[iKPISortColumn].ToString());
                            switch (Strings.UCase(sPrefix))
                            {
                                case "CUR_":
                                    {
                                        sVal = Strings.FormatCurrency(sVal, 0);
                                        break;
                                    }

                                case "SI__":
                                    {
                                        sVal = Strings.FormatNumber(sVal, 0);
                                        break;
                                    }

                                case "SR__":
                                    {
                                        sVal = Strings.FormatNumber(sVal, 2);
                                        break;
                                    }

                                default:
                                    {
                                        break;
                                    }
                            }
                        }
                        drrResult[i] = sVal;
                    }    // For i As Integer = iHighestSortCount To dtSchema.Rows.Count - 1 'Columns in dtResult
                }    // For Each drResult In dtResult.Rows

                // Translate sort IDs to friendly names   
                string sColumnName = "";
                DataTable dtLookup = new DataTable();
                DataView dv = new DataView();
                // For each column, get column name and friendly names for each record
                foreach (DataColumn col in dtResult.Columns)
                {
                    // Get column name
                    sColumnName = col.ColumnName;
                    // Is this TXT_KPIName, TXT_[FileName], or TXT_DateInt
                    switch (Strings.UCase(sColumnName))
                    {
                        case "TXT_KPINAME":
                            {
                                // get values from dtKPI 
                                dtLookup = dtKPI;
                                break;
                            }

                        case "TXT_DATEINT":
                            {
                                // get values from dtDateInt\
                                dtLookup = dtDateInt;
                                break;
                            }

                        default:
                            {
                                // File
                                // dtFile1, dtFile2, ...
                                if (Strings.Left(sColumnName, 4) != "CUR_")
                                {
                                    sFileName = Strings.Right(sColumnName, Strings.Len(sColumnName) - Strings.InStr(sColumnName, "_"));
                                    if (dtFile1 == null)
                                    {
                                        if (dtFile1.TableName == sFileName)
                                            dtLookup = dtFile1;
                                    }
                                    if (dtFile2 == null)
                                    {
                                        if (dtFile2.TableName == sFileName)
                                            dtLookup = dtFile2;
                                    }
                                    if (dtFile3 == null)
                                    {
                                        if (dtFile3.TableName == sFileName)
                                            dtLookup = dtFile3;
                                    }
                                    if (dtFile4 == null)
                                    {
                                        if (dtFile4.TableName == sFileName)
                                            dtLookup = dtFile4;
                                    }
                                }

                                break;
                            }
                    }
                    // Loop rows
                    foreach (DataRow dr in dtResult.Rows)
                    {
                        // Get KPI 'Name', Date int name, SYS_Name
                        switch (Strings.UCase(sColumnName))
                        {
                            case "TXT_KPINAME":
                                {
                                    // get values from dtKPI 
                                    dv = dtLookup.DefaultView;
                                    dv.RowFilter = "ID='" + dr[col] + "'";
                                    dr[col] = dv[0]["Name"];
                                    break;
                                }

                            case "TXT_DATEINT":
                                {
                                    break;
                                }

                            default:
                                {
                                    // File: sColumnName like 'TXT_US'
                                    if (Strings.Left(sColumnName, 4) != "CUR_")
                                    {
                                        dv = dtLookup.DefaultView;
                                        dv.RowFilter = "GID_ID='" + dr[col] + "'";

                                        // *** Problem here 9/22
                                        dr[col] = dv[0]["SYS_Name"];
                                    }

                                    break;
                                }
                        }
                    }
                }

                // testing: save dt in session
                goP.SetVar("DW_dtResult", dtResult);

                // for testing
                dtResult.TableName = sDWFileName;
                bool bResult = DW_UploadResultTable(dtResult);
                par_iValid = 3;
                string par_sDelim = "|";
                if (bResult)
                    goMeta.LineWrite("", sDWPageID, "LASTRUNTIME", goTR.DateTimeToString(goTR.NowLocal(), "", "", ref par_iValid, ref par_sDelim), ref par_oConnection);
            }
            // Dim dsResult As New DataSet
            // dsResult.Tables.Add(dtResult)
            // dsResult.DataSetName = "DS"
            // dsResult.WriteXml("D:\Customer\GPS\SalesGPS\Downloads\TestScorecard.xml", XmlWriteMode.WriteSchema)

            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public DataTable DW_GetKPITable(string sDWPageID)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                DataTable dt = new DataTable();
                DataRow dr;
                string sPage = goMeta.PageRead("GLOBAL", sDWPageID);
                par_iValid = 4;
                int iKPICount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sPage, "KPICOUNT"), "", ref par_iValid));

                // create dt columns
                string sCol = "ID,Name,Property";
                string[] aCol = Strings.Split(sCol, ",");
                for (int iCol = 0; iCol <= aCol.GetUpperBound(0); iCol++)
                {
                    DataColumn dc = new DataColumn();
                    dc.ColumnName = aCol[iCol];
                    dt.Columns.Add(dc);
                }

                for (int i = 1; i <= iKPICount; i++)
                {
                    dr = dt.NewRow();
                    dr["ID"] = goTR.StrRead(sPage, "KPI" + i);
                    dr["Name"] = goMeta.LineRead("GLOBAL", goTR.StrRead(sPage, "KPI" + i), "NAME");
                    dr["Property"] = goMeta.LineRead("GLOBAL", goTR.StrRead(sPage, "KPI" + i), "FORMULA");
                    dt.Rows.Add(dr);
                }

                dt.TableName = "KPI";

                return dt;
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
                return null;
            }
        }
        public DataTable DW_GetDateIntTable(string sDWPageID)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Metadata page
            string sPage;
            string sStartDate;
            string sEndDate;
            string sInterval;
            // ----

            int iColIndex = 0;
            int iIntervals = 0;
            int iStartMonth = 0;
            int iStartYear = 0;
            int iStartQuarter = 0;
            int iEndMonth = 0;
            int iEndQuarter = 0;
            int iEndYear = 0;
            int iMonths = 0;
            int iQuarters = 0;
            int iCurrentMonth = 0;
            int iCurrentQuarter = 0;
            int iCurrentYear = 0;
            string sCurrentStartDate;
            string sCurrentEndDate;
            string sQuarterName = "";

            DataTable dt = new DataTable();
            DataRow dr;
            string sValue = "";
            string sProperty = "";

            try
            {
                // Create table columns
                string sCol = "ID,Property";
                string[] aCol = Strings.Split(sCol, ",");
                DataColumn dc;
                for (int i = 0; i <= aCol.GetUpperBound(0); i++)
                {
                    dc = new DataColumn();
                    dc.ColumnName = aCol[i];
                    dt.Columns.Add(dc);
                }

                sPage = goMeta.PageRead("", sDWPageID);
                sStartDate = goTR.StrRead(sPage, "STARTDATE");
                sEndDate = goTR.StrRead(sPage, "ENDDATE");
                sInterval = goTR.StrRead(sPage, "DATEINTERVAL");

                // Get start/end values
                if (goTR.IsDate(sStartDate))
                {
                    iStartMonth = Convert.ToInt32(goTR.StringToNum(goTR.GetMonth(DateTime.Parse(sStartDate)), "", ref par_iValid));
                    iStartYear = Convert.ToInt32(goTR.StringToNum(goTR.GetYear(DateTime.Parse(sStartDate)), "", ref par_iValid));
                    iStartQuarter = Convert.ToInt32(goTR.StringToNum(goTR.GetQuarter(DateTime.Parse(sStartDate)), "", ref par_iValid));
                    iEndMonth = Convert.ToInt32(goTR.StringToNum(goTR.GetMonth(DateTime.Parse(sEndDate)), "", ref par_iValid));
                    iEndYear = Convert.ToInt32(goTR.StringToNum(goTR.GetYear(DateTime.Parse(sEndDate)), "", ref par_iValid));
                    iEndQuarter = Convert.ToInt32(goTR.StringToNum(goTR.GetQuarter(DateTime.Parse(sEndDate)), "", ref par_iValid));
                    iMonths = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Month, goTR.StringToDate(sStartDate, "", ref par_iValid), goTR.StringToDate(sEndDate, "", ref par_iValid)) + 1);
                    iQuarters = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Quarter, goTR.StringToDate(sStartDate, "", ref par_iValid), goTR.StringToDate(sEndDate, "", ref par_iValid)) + 1);
                }
                else
                {
                }

                // How many intervals in the date range?
                switch (Strings.UCase(sInterval))
                {
                    case "MONTH":
                        {
                            iIntervals = iMonths;
                            iCurrentMonth = iStartMonth;
                            iCurrentYear = iStartYear;
                            for (int i = 1; i <= iIntervals; i++)
                            {
                                sValue = "";
                                // What is the 'name' of the interval? Month abbreviation plus 2 digit year
                                sValue += DateAndTime.MonthName(iCurrentMonth, true);
                                // What is the current year?
                                sValue += Strings.Right(goTR.NumToString(iCurrentYear), 2);

                                sCurrentStartDate = iCurrentYear.ToString() + "-" + iCurrentMonth.ToString() + "-" + "01";
                                sCurrentEndDate = iCurrentYear.ToString() + "-" + iCurrentMonth.ToString() + "-" + DW_GetLastDayOfMonth(iCurrentMonth);

                                dr = dt.NewRow();
                                dr["ID"] = sValue;
                                dr["Property"] = sCurrentStartDate + "|" + sCurrentEndDate;
                                dt.Rows.Add(dr);

                                // Advance to next year if this is month 12
                                if (iCurrentMonth == 12)
                                {
                                    iCurrentYear += 1;
                                    iCurrentMonth = 1;
                                }
                                else
                                    iCurrentMonth += 1;
                                iColIndex += 1;
                            }

                            break;
                        }

                    case "QUARTER":
                        {
                            iIntervals = iQuarters;
                            iCurrentQuarter = iStartQuarter;
                            iCurrentYear = iStartYear;
                            for (int i = 1; i <= iIntervals; i++)
                            {

                                // What is the 'name' of the interval? 'Q' plus 2 digit quarter plus 2 digit year
                                switch (iCurrentQuarter)
                                {
                                    case 1:
                                    case 2:
                                    case 3:
                                        {
                                            sQuarterName = "Q1";
                                            break;
                                        }

                                    case 4:
                                    case 5:
                                    case 6:
                                        {
                                            sQuarterName = "Q2";
                                            break;
                                        }

                                    case 7:
                                    case 8:
                                    case 9:
                                        {
                                            sQuarterName = "Q3";
                                            break;
                                        }

                                    case 10:
                                    case 11:
                                    case 12:
                                        {
                                            sQuarterName = "Q4";
                                            break;
                                        }
                                }
                                sValue += sQuarterName;
                                // What is the current year?
                                sValue += Strings.Right(goTR.NumToString(iCurrentYear), 2);

                                sCurrentStartDate = iCurrentYear.ToString() + "-" + DW_GetFirstMonthOfQuarter(iCurrentQuarter) + "-" + "01";
                                sCurrentEndDate = iCurrentYear.ToString() + "-" + DW_GetLastMonthOfQuarter(iCurrentQuarter) + "-" + DW_GetLastDayOfMonth(int.Parse(DW_GetLastMonthOfQuarter(iCurrentQuarter)));

                                dr = dt.NewRow();
                                dr["ID"] = sValue;
                                dr["Property"] = sCurrentStartDate + "|" + sCurrentEndDate;
                                dt.Rows.Add(dr);

                                // Advance to next year if this is month 12
                                if (iCurrentQuarter == 4)
                                {
                                    iCurrentQuarter = 1;
                                    iCurrentYear += 1;
                                }
                                else
                                    iCurrentQuarter += 1;
                                iColIndex += 1;
                            }

                            break;
                        }

                    case "YEAR":
                        {
                            break;
                        }
                }

                return dt;
            }

            // ======= OLD ==============
            // Dim dt As New DataTable
            // Dim dr As DataRow
            // Dim sPage As String = ""

            // 'Date vars
            // Dim sDateRange As String = ""
            // Dim sDateInterval As String = ""
            // Dim iIntCount As Integer = 0
            // Dim sIntervalStartDate As String
            // Dim sIntervalEndDate As String
            // Dim sMonthStart As String
            // Dim sMonthEnd As String
            // Dim sYearStart As String
            // Dim sYearEnd As String
            // Dim sDayStart As String
            // Dim sDayEnd As String
            // Dim sStartDate As String
            // Dim sEndDate As String

            // Try

            // sPage = goMeta.PageRead("GLOBAL", sDWPageID)
            // sDateRange = goTR.StrRead(sPage, "DATERANGE")
            // sDateInterval = goTR.StrRead(sPage, "DATEINTERVAL")

            // 'create dt columns: ID=1,2,3  Name=Jan '10, Q2 '10, 2010   Property=2010-01-01|2010-01-31
            // Dim sCol As String = "ID,Name,Property"
            // Dim aCol() As String = Split(sCol, ",")
            // For iCol As Integer = 0 To aCol.GetUpperBound(0)
            // Dim dc As New DataColumn
            // dc.ColumnName = aCol(iCol)
            // dt.Columns.Add(dc)
            // Next

            // Select Case UCase(sDateRange)
            // Case "THISMONTH"
            // sYearStart = Year(Now)
            // sYearEnd = sYearStart
            // sMonthStart = goTR.Pad(Month(Now), 2, "0", "L")
            // sMonthEnd = sMonthStart
            // sDayStart = "01"
            // Select Case sMonthStart
            // Case 1, 3, 5, 7, 8, 10, 12
            // sDayEnd = "31"
            // Case 4, 6, 9, 11
            // sDayEnd = "30"
            // Case 2
            // sDayEnd = "28"
            // End Select
            // Select Case sDateInterval
            // Case "MONTH"
            // iIntCount += 1
            // sStartDate = sYearStart & "-" & sMonthStart & "-" & sDayStart
            // sEndDate = sYearEnd & "-" & sMonthEnd & "-" & sDayEnd
            // dr = dt.NewRow
            // dr("ID") = iIntCount
            // dr("Name") = MonthName(Month(Now), True) & " '" & Right(Year(Now), 2)
            // dr("Property") = sStartDate & "|" & sEndDate   'StartDate|EndDate
            // dt.Rows.Add(dr)
            // Case "DAY"
            // 'how many days in the month? - sDayEnd
            // For iDayCount As Integer = 1 To goTR.StringToNum(sDayEnd)
            // iIntCount += 1
            // sStartDate = sYearStart & "-" & sMonthStart & "-" & goTR.Pad(iDayCount.ToString, 2, "0", "L")
            // sEndDate = sStartDate
            // dr = dt.NewRow
            // dr("ID") = iIntCount
            // dr("Name") = iDayCount.ToString
            // dr("Property") = sStartDate & "|" & sEndDate   'StartDate|EndDate
            // dt.Rows.Add(dr)
            // Next


            // End Select

            // Case "THISQUARTER"
            // 'iMonth = Month(Now)
            // ''what months are in this quarter?
            // 'Select Case iMonth
            // '    Case 1, 2, 3
            // '        iFirstMonthinQuarter = 1
            // '    Case 4, 5, 6
            // '        iFirstMonthinQuarter = 4
            // '    Case 7, 8, 9
            // '        iFirstMonthinQuarter = 7
            // '    Case 10, 11, 12
            // '        iFirstMonthinQuarter = 10
            // 'End Select

            // 'For i As Integer = iFirstMonthinQuarter To iMonth
            // '    sMonth = MonthName(i, True)
            // '    oCollection.Add(sMonth & " '" & Right(Year(Now), 2))
            // 'Next
            // Case "THISYEAR"
            // 'iMonth = Month(Now)
            // 'For i As Integer = 1 To iMonth
            // '    sMonth = MonthName(i, True)
            // '    oCollection.Add(sMonth & " '" & Right(Year(Now), 2))
            // 'Next

            // End Select


            // For i As Integer = 1 To cIntervals.Count
            // dr = dt.NewRow
            // dr("ID") = 
            // dr("Name") = 
            // dr("Property") = 
            // dt.Rows.Add(dr)
            // Next

            // dt.TableName = "DATEINT"

            // Return dt


            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
                return null;
            }
        }
        public DataTable DW_GetFileTable(string sDWPageID, int iSortIndex)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                string sPage = goMeta.PageRead("GLOBAL", sDWPageID);
                string sFileName = goTR.StrRead(sPage, "SORT" + iSortIndex + "FILE");
                string sFilter = goTR.StrRead(sPage, "SORT" + iSortIndex + "FILTER");
                DataTable dt = new DataTable();
                clRowSet oRS;

                // testing
                if (sFileName == "US" & sFilter == "")
                    sFilter = "CHK_ActiveField=1";
                // '''''

                oRS = new clRowSet(sFileName, 3, sFilter, "SYS_NAME", "GID_ID, SYS_NAME");
                if (oRS.GetFirst()== 1)
                {
                    oRS.ToTable();
                    dt = oRS.dtTransTable;
                }

                dt.TableName = sFileName;

                return dt;
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
                return null;
            }
        }
        public string DW_GetKPIProperty(string sKPIID, string sKPIProperty)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                string sPage = goMeta.PageRead("GLOBAL", sKPIID);
                string sFormula = goTR.StrRead(sPage, "FORMULA");
                string sFile = "";

                switch (Strings.UCase(sKPIProperty))
                {
                    case "FORMULA":
                        {
                            return sFormula;
                        }

                    case "FILE":
                        {
                            // 'Parse formula to get file
                            // If sFormula.Contains(".") Then
                            // sFile = Left(sFormula, InStr(sFormula, ".") - 1)
                            // sFile = Right(sFile, 2)
                            // End If
                            sFile = goTR.StrRead(sPage, "FILE");

                            return sFile;
                        }
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
                return "";
            }

            return ""; // TLD 7/24/2012?
        }
        public string DW_GetLinkNameFromGoals(string sFile1, string sFile2)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sOTHGoalPage = "";
            int iCount;
            string sValue;
            string[] aValue;
            string sFile1Index = "";
            string sFile2Index = "";
            string sLinkName;

            try
            {
                sOTHGoalPage = goMeta.PageRead("GLOBAL", "OTH_GOALS");

                iCount = int.Parse(goTR.StrRead(sOTHGoalPage, "GOALFILECOUNT"));
                for (int i = 1; i <= iCount; i++)
                {
                    sValue = goTR.StrRead(sOTHGoalPage, "GOALFILE" + goTR.Pad(i.ToString(), 2, "0", "L"));
                    aValue = Strings.Split(sValue, ",");
                    if (aValue[0] == sFile1)
                    {
                        sFile1Index = goTR.Pad(i.ToString(), 2, "0", "L");
                        break;
                    }
                }

                iCount = int.Parse(goTR.StrRead(sOTHGoalPage, "MENUCOUNT"));
                for (int i = 1; i <= iCount; i++)
                {
                    sValue = goTR.StrRead(sOTHGoalPage, "MENU" + goTR.Pad(i.ToString(), 2, "0", "L"));
                    aValue = Strings.Split(sValue, ",");
                    if (aValue[0] == sFile2)
                    {
                        sFile2Index = goTR.Pad(i.ToString(), 2, "0", "L");
                        break;
                    }
                }

                sLinkName = goTR.StrRead(sOTHGoalPage, "GOALLINK" + sFile1Index + sFile2Index);
                if (sLinkName != "")
                    sLinkName = Strings.Split(sLinkName, ",")[2];

                return sLinkName;
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
                return "";
            }

            return "";
        }
        public string DW_GetDateFieldFromGoals(string sFile1)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sOTHGoalPage = "";
            int iCount;
            string sValue = "";
            string[] aValue;
            string sFile1Index = "";
            string sDateField = "";

            try
            {

                // sFile1 = Goal file
                // sFile2 = Menu file

                sOTHGoalPage = goMeta.PageRead("GLOBAL", "OTH_GOALS");
                iCount = int.Parse(goTR.StrRead(sOTHGoalPage, "GOALFILECOUNT"));
                for (int i = 1; i <= iCount; i++)
                {
                    sValue = goTR.StrRead(sOTHGoalPage, "GOALFILE" + goTR.Pad(i.ToString(), 2, "0", "L"));
                    aValue = Strings.Split(sValue, ",");
                    if (aValue[0] == sFile1)
                    {
                        sFile1Index = goTR.Pad(i.ToString(), 2, "0", "L");
                        break;
                    }
                }

                sDateField = goTR.StrRead(sOTHGoalPage, "GOALFILEDATE" + sFile1Index);
                return sDateField;
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return ""; // TLD 7/24/2012?
        }
        public string DW_GetLastDayOfMonth(int iMonthNum)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                string sDayNum = "";

                switch (iMonthNum)
                {
                    case 1:
                    case 3:
                    case 5:
                    case 7:
                    case 8:
                    case 10:
                    case 12:
                        {
                            sDayNum = "31";
                            break;
                        }

                    case 4:
                    case 6:
                    case 9:
                    case 11:
                        {
                            sDayNum = "30";
                            break;
                        }

                    case 2:
                        {
                            sDayNum = "28";
                            break;
                        }

                    default:
                        {
                            break;
                        }
                }

                return sDayNum;
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return ""; // TLD 7/24/2012?
        }
        public string DW_GetFirstMonthOfQuarter(int iMonthNum)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                string sMonthNum = "";

                switch (iMonthNum)
                {
                    case 1:
                        {
                            sMonthNum = "1";
                            break;
                        }

                    case 2:
                        {
                            sMonthNum = "4";
                            break;
                        }

                    case 3:
                        {
                            sMonthNum = "7";
                            break;
                        }

                    case 4:
                        {
                            sMonthNum = "10";
                            break;
                        }

                    default:
                        {
                            break;
                        }
                }

                return sMonthNum;
            }

            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return "";
        }
        public string DW_GetLastMonthOfQuarter(int iMonthNum)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                string sMonthNum = "";

                switch (iMonthNum)
                {
                    case 1:
                        {
                            sMonthNum = "3";
                            break;
                        }

                    case 2:
                        {
                            sMonthNum = "6";
                            break;
                        }

                    case 3:
                        {
                            sMonthNum = "9";
                            break;
                        }

                    case 4:
                        {
                            sMonthNum = "12";
                            break;
                        }

                    default:
                        {
                            break;
                        }
                }

                return sMonthNum;
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return ""; // TLD 7/24/2012?
        }
        public bool DW_UploadResultTable(DataTable dtResult)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // This is separate function since calcs can be sent to scorecard and not written to database


            clRowSet oRS;

            try
            {
                foreach (DataRow dr in dtResult.Rows)
                {
                    oRS = new clRowSet(dtResult.TableName, 2);
                    foreach (DataColumn column in dtResult.Columns)
                        oRS.SetFieldVal(column.ColumnName, dr[column]);
                    // GenSQL needs fix for [0A]
                    oRS.Commit();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool GL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PJ 5/20/10: Added TE to sFiles array

            clRowSet doRS = (clRowSet)par_doCallingObject;

            DateTime dtBlankDate = Convert.ToDateTime("1753-01-02");

            try
            {
                // Fill For file link
                clRowSet oRS;
                string sFilename;
                string sFiles = "CO,DV,GR,LO,PD,US,VE,TE";
                string[] aFiles = Strings.Split(sFiles, ",");
                doRS.ClearLinkAll("LNK_For_FI");
                for (int i = 0; i <= aFiles.GetUpperBound(0); i++)
                {
                    sFilename = aFiles[i];
                    if (doRS.GetLinkCount("LNK_FOR_" + sFilename) > 0)
                    {
                        oRS = new clRowSet("FI", 3, "TXT_FileName='" + sFilename + "'", "", "GID_ID");
                        if (oRS.GetFirst()== 1)
                            doRS.SetFieldVal("LNK_FOR_FI", oRS.GetFieldVal("GID_ID"));
                    }
                }
            }

            // 'Clooge: If no filter (links) on GL form from above For loop, then the GL is for 'ALL' and needs to be set to 'Report' file
            // '       so that it can be picked up by the scorecard
            // If doRS.GetFieldVal("LNK_FOR_FI") = "" Then
            // doRS.SetFieldVal("LNK_FOR_FI", "e25421b8-3b74-46c4-4649-98dd0142a7c1", 2)
            // End If

            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            par_bRunNext = false;

            par_doCallingObject = doRS;
            return true;
        }
        public bool GoalsCreateRecordsFromDatatable(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                DataSet ds = (DataSet)par_doCallingObject;
                string sFile = ds.Tables[0].TableName;
                string sFileID = "";
                // Convert to file name, not label

                // Associate column headers in Excel to field names on GL form
                Hashtable oHashFieldName = new Hashtable();
                oHashFieldName.Add("Sales Visits", "LI__GoalAC");
                oHashFieldName.Add("Lead Goal (Count)", "LI__GoalLE");
                oHashFieldName.Add("Opps Goal (Count)", "LI__GoalOP");
                oHashFieldName.Add("Opps Goal", "CUR_GoalOP");
                oHashFieldName.Add("Quotes Goal (Count)", "LI__GoalQT");
                oHashFieldName.Add("Quote Lines Goal", "CUR_GoalQL");
                oHashFieldName.Add("Orders Goal", "CUR_GoalOH");
                oHashFieldName.Add("Orders Goal (Count)", "LI__GoalOH");
                oHashFieldName.Add("Activity Goal (Count)", "LI__GoalAC");
                oHashFieldName.Add("Appt Goal (Count)", "LI__GoalAP");

                // Get GID_ID, BI__ID from file
                clRowSet qRS;
                clRowSet fiRS;
                Hashtable oHash = new Hashtable();
                if (sFile != "ALL")
                {
                    // create hash table of BI__ID, GID_ID for the records in the file
                    qRS = new clRowSet(sFile, 3, null, null, "GID_ID, BI__ID");
                    if (qRS.GetFirst() == 1)
                    {
                        do
                        {
                            oHash.Add(qRS.GetFieldVal("BI__ID"), qRS.GetFieldVal("GID_ID"));
                            if (qRS.GetNext()== 0)
                                break;
                        }
                        while (true);
                    }
                    // link to file
                    fiRS = new clRowSet("FI", 3, "TXT_FileName='" + sFile + "'", null, "GID_ID");
                    if (fiRS.GetFirst() == 1)
                        sFileID = Convert.ToString(fiRS.GetFieldVal("GID_ID"));
                }
                // loop through import dt and create GL records
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    // see if record exists
                    if (sFile != "ALL")
                        qRS = new clRowSet("GL", 1, "LNK_For_" + sFile + "='" + oHash[dr["Unique ID"]] + "'");
                    else
                        qRS = new clRowSet("GL", 1, "TXT_GoalName='ALL'");
                    if (qRS.GetFirst() == 0)
                        qRS = new clRowSet("GL", 2);
                    // Link to 'filter' category
                    if (sFile != "ALL")
                        qRS.SetFieldVal("LNK_For_" + sFile, oHash[dr["Unique ID"]]);
                    // Fill fields
                    qRS.SetFieldVal("TXT_GoalName", dr[1]);
                    for (int i = 2; i <= ds.Tables[0].Columns.Count - 1; i++)    // first 2 cols are ID and Name
                    {
                        qRS.SetFieldVal(oHashFieldName[ds.Tables[0].Columns[i].ColumnName].ToString(), dr[i].ToString());
                        if (ds.Tables[0].Columns[i].ColumnName == "Sales Visits")
                            qRS.SetFieldVal("MLS_ActivityType", 11, 2);
                    }
                    // Link to FI
                    if (sFile != "ALL")
                        qRS.SetFieldVal("LNK_For_FI", sFileID);
                    int iResult = qRS.Commit();
                }

                par_doCallingObject = ds;
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool Goals_OpenScorecard(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                goUI.OpenURLExternal("../Pages/cus_Scorecard.aspx", "Selltis", "height=750,width=1200,left=100,top=100,status=yes,location=no,toolbar=yes,resizable=yes,titlebar=no,dependent=yes");
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool Goals_OpenWOP(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                goUI.OpenURLExternal("../Pages/cus_wopGPS.aspx", "Selltis", "height=500,width=600,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2)) == 11 & Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_PD")) == "")
            {
                goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "LNK_RELATED_PD"), "", "", "", "", "", "", "", "", "LNK_RELATED_PD");
                par_doCallingObject = doForm;
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormControlOnChange_MLS_TYPE_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2)) == 11)
            {
                string sColor = goP.GetVar("sMandatoryFieldColor")?.ToString();
                doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", sColor);
            }
            else
                doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", "BLACK");

            par_doCallingObject = doForm;
            return true;
        }
        public bool AutoAlertEveryDay_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter in all rowsets to get all users' private recs
            // MI 10/19/07 started converting the script to be aware of the start of the day depending on the user's time zone
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, true);

            // TLD 7/6/2010 Prevent main alerts from running
            // Only wants the Op Alert to run for now
            goTR.StrWrite(ref par_sSections, "AlertOverdueActivities", "0");
            goTR.StrWrite(ref par_sSections, "AlertOverdueContacts", "0");
            goTR.StrWrite(ref par_sSections, "AlertOverdueCompanies", "0");
            goTR.StrWrite(ref par_sSections, "AlertOverdueLeads", "0");
            goTR.StrWrite(ref par_sSections, "AlertCorrespondenceInBox", "0");
            goTR.StrWrite(ref par_sSections, "AlertOverdueProjects", "0");
            goTR.StrWrite(ref par_sSections, "AlertOverdueQuotes", "0");
            goTR.StrWrite(ref par_sSections, "AlertOverdueToDos", "0");
            goTR.StrWrite(ref par_sSections, "AlertExpenseApprovalAndReimbReminder", "0");

            // VS 04132015 TKT#476 : overdue opps, alert to trigger 5 days before the exp close date
            clRowSet doRS;
            clRowSet TrialRS;
            string sResult;
            bool bMore = true;
            string sCurrentUser = "";
            PublicDomain.TzTimeZone zone;
            DateTime dtUsersToday;
            DateTime dtDateTime;
            string sDateTime;
            string sPointers;
            string sPointerDateTime;
            DateTime dtPointerDate;

            clRowSet rsUsers = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVEFIELD=1", "", "GID_ID, MLS_Type");
            // No active users                       
            if (rsUsers.Count() < 1)
                bMore = false;

            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED");

            while (bMore == true)
            {
                sCurrentUser = Convert.ToString(rsUsers.GetFieldVal("GID_ID", clC.SELL_FRIENDLY));

                zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                // Get user's 'now' and 'today at midnight' in the user's time zone
                dtUsersToday = zone.ToLocalTime(goTR.NowUTC()).Date;                  // Midnight on user's current date in his/her time zone

                // -------- Skip this user if already processed today --------
                // Get the 'last processed' date (no time) as a local datetime
                // Pointers are written as local datetimes in user's last login time zone.
                sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, sCurrentUser, "", false), 10);
                par_iValid = 3;
                if (sPointerDateTime == "")
                    // Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                    dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
                else
                    dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
                dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);

                // DEBUG ==> Remove
                // goLog.Log(sProc, "    Pointer date: '" & goTR.DateTimeToSysString(dtPointerDate) & "' User's date: '" & goTR.DateTimeToSysString(dtUsersToday) & "'", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                if (dtPointerDate == dtUsersToday)
                    goto ProcessNextUserNow;

                // ---------- Overdue Opps On ExpCloseDate -----------
                // --------- Set 'tomorrow' -----------
                // Set user's 'tomorrow at midnight' as local datetime for filtering
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                // dtDateTime = goTR.AddDay(dtDateTime, 1)
                dtDateTime = goTR.AddDay(dtDateTime, 5); // Alert before 5 Days of ExpectedCloseDate
                par_sSections = "|";
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sSections);

                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueOpps", true))
                {
                    // Opp:Overdue Opps
                    // sDateTime: 5days from today
                    // *** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("OP", 3, "MLS_STATUS=0 AND DTT_EXPCLOSEDATE<'" + sDateTime + "' AND LNK_CreditedTo_US='" + sCurrentUser + "'", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                    if (doRS.GetFirst() == 1)
                        sResult = goUI.AddAlert("Overdue Opps", "OPENDESKTOP", "DSK_9F3AA1F6-EADE-46DB-5858-A47901193C39", sCurrentUser, "AlertOn.gif").ToString();
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }

                // ---------- Overdue Opps On NextActionDate -----------
                // --------- Set 'tomorrow' -----------
                // Change Alert name from Overdue Opps to Overdue Opps Action
                // Set user's 'tomorrow at midnight' as local datetime for filtering
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                dtDateTime = goTR.AddDay(dtDateTime, 1);
                par_iValid = 3;
                par_sSections = "|";
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sSections);

                // ---------- Opps to Review -----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueOpps", true))
                {
                    // Opp:ReviewOverdueAlarm
                    // sDateTime: tomorrow
                    // CS 06242015 Below line was causing this not to run except for the first user.
                    // goTR.StrWrite(par_sSections, "AlertOverdueOpps", "0")
                    // *** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("OP", 3, "MLS_STATUS=0 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND LNK_CreditedTo_US='" + sCurrentUser + "'", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                    if (doRS.GetFirst() == 1)
                        sResult = goUI.AddAlert("Overdue Opps Action", "OPENDESKTOP", "DSK_2004060109262365125C_S 00016XX", sCurrentUser, "OPP16.gif").ToString();
                    // CS 1/6/12
                    // goLog.Log(sProc, "after opps overdue")
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }


                ProcessNextUserNow:
                ;
                if (rsUsers.GetNext()== 0)
                    bMore = false;
            }

            rsUsers = null/* TODO Change to default(_) if this is not a reference type */;
            // CS 06242015 Added here from above
            goTR.StrWrite(ref par_sSections, "AlertOverdueOpps", "0");
            return true;
        }
        public bool AutoAlertEveryNSecs(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 4/10/08 Added True for par_bGetAllUsersUnsharedRecs param in rowset declarations to get all private records,
            // not only the ones created by the current user.
            // MI 10/26/07 Fixed alerts not firing when set to few minutes to start time or if end time = start time.
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, true);

            // TLD 7/6/2010 Only wants OP alerts to run
            par_bRunNext = false;

            return true;
        }
        public bool AutoCOUpdate(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 7/24/2012
            // PURPOSE:
            // Called by agent to update 3 custom date fields that
            // record latest AC of type Sales Visit, latest OP and latest Quote
            // A_01_EXECUTE=AutoCOUpdate
            // A_01_OBJSHARED = 1
            // A_01_TYPE = RUNSCRIPT
            // A_ORDER=1,
            // ACTIONS=1,
            // ACTIVE = 1
            // E_TM_HOUR = 19 '7:00 PM
            // E_TM_INTERVAL = 3 'Every day
            // E_TM_MINUTE = 0 'top of hour
            // EVENT=TIMER
            // US_NAME=AutoCOUpdate
            // US_PURPOSE=Runs daily Update of Company records
            // SORTVALUE1=TIMER_ACTIVE

            long lBiID = 0;
            long lastBiId = 0;
            clRowSet doRS;
            clRowSet doNewRS;
            int iCount = 0;
            bool bUpdateCO = false;
            par_iValid = 3;
            string par_sDelim = "|";
            string sNowDate = goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim);
            string sRecID = "";
            string sWOP = goMeta.PageRead("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED");
            int iFailedOther = 0;
            int iFailedPerm = 0;
            int iFailedTotal = 0;
            int iSuccess = 0;

            try
            {
                do
                {
                    doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", 50, null, null, null, null, null, true, true, true);
                    iCount = iCount + Convert.ToInt32(doRS.Count());
                    if (doRS.GetFirst() == 1)
                    {
                        lBiID = Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                        do
                        {
                            bUpdateCO = false;
                            sRecID = doRS.GetCurrentRecID().ToString();

                            // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                            if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                            {
                                doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            // TLD 5/18/2011 -----------Added to include date of latest OP
                            if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                            {
                                doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            // TLD 5/18/2011 -----------Added to include date of latest QT
                            if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                            {
                                doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }

                            // Update CO
                            if (bUpdateCO == true)
                            {
                                if (doRS.Commit() == 0)
                                {
                                    if (goErr.GetLastError("NUMBER") == "E47250")
                                    {
                                        // Commit failed b/c user has no permissions to edit record; log it, but proceed
                                        iFailedPerm = iFailedPerm + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + Convert.ToString(doRS.GetFieldVal("TXT_CompanyName")) + " due to permissions.", 1, false, true);
                                    }
                                    else
                                    {
                                        // Commit failed for some other reason.
                                        iFailedOther = iFailedOther + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + Convert.ToString(doRS.GetFieldVal("TXT_CompanyName")) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                    }
                                }
                            }

                            if (doRS.GetNext() == 0)
                                break;
                        }
                        while (true);
                        lBiID = Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    }
                    else
                        break;
                }
                while (true);// until set to true below// testing// testing// get last BI__ID processed

                // Check once more for any newly added records
                doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", -1, "", "", "", "", "", true, true, true);
                iCount = iCount + Convert.ToInt32(doRS.Count());
                if (doRS.GetFirst() == 1)
                {
                    do
                    {
                        bUpdateCO = false;
                        sRecID = doRS.GetCurrentRecID().ToString();

                        // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                        if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                        {
                            doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        // TLD 5/18/2011 -----------Added to include date of latest OP
                        if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                        {
                            doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        // TLD 5/18/2011 -----------Added to include date of latest QT
                        if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                        {
                            doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }

                        // Update CO
                        if (bUpdateCO == true)
                        {
                            if (doRS.Commit() == 0)
                            {
                                if (goErr.GetLastError("NUMBER") == "E47250")
                                {
                                    // Commit failed b/c user has no permissions to edit record; log it, but proceed
                                    iFailedPerm = iFailedPerm + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + Convert.ToString(doRS.GetFieldVal("TXT_CompanyName")) + " due to permissions.", 1, false, true);
                                }
                                else
                                {
                                    // Commit failed for some other reason.
                                    iFailedOther = iFailedOther + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + Convert.ToString(doRS.GetFieldVal("TXT_CompanyName")) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                            }
                        }

                        if (doRS.GetNext() == 0)
                            break;
                    }
                    while (true);// until set to true below// testing// testing

                    lBiID = Convert.ToInt64(doRS.GetFieldVal("BI__ID")); // get last bi__id processed        
                }
                iFailedTotal = iFailedOther + iFailedPerm;
                iSuccess = iCount - iFailedTotal;

                // Write to WOP
                par_iValid = 3;
                par_sDelim = "|";
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Started " + sNowDate + " and Completed " + goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim) + " with " + iSuccess + " successful updates; " + iFailedPerm + " failed updates due to permissions; " + iFailedOther + " total failed updates.");
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);

                iCount = 0;
                iFailedOther = 0;
                iFailedPerm = 0;
                iFailedTotal = 0;
                iSuccess = 0;
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                lBiID = 0;
            }
            catch (Exception ex)
            {
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Failed at Record " + sRecID + " " + goErr.GetLastError("NUMBER"));
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);
            }

            return true;
        }
        public bool Auto_ConvertCurrencies_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: File
            // par_s2: Field 1 Name
            // par_s3: Unused.
            // par_s4: OP probability
            // par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 8/4/2014 Added to conver Dollar, Sterling to Euro
            // Currently called from OP only
            par_bRunNext = false;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            if (doRS.Count() > 0)
            {
                decimal cValue = 0; // CUR_Value from OP
                string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");
                decimal cExchRate = 0;
                double rProb = 0;
                int iCurr;

                cValue = Convert.ToDecimal(doRS.GetFieldVal(par_s2, 1));

                // OP CUR_ValueIndexEuro calc
                par_iValid = 4;
                if (goTR.StringToNum(par_s4, "", ref par_iValid) == 1)
                {
                    rProb = goTR.StringToNum(par_s4, "", ref par_iValid);
                    if (Information.IsNumeric(rProb))
                        rProb = rProb / 100; // convert to decimal
                    else
                        rProb = 0;// set to 0?
                }

                // 'If MLS_Currency changed on form, the rowset calling this may not have the current value on the form.
                // '   Messagebox event from MLS_Currency change sets var:
                // If goP.GetVar("OP_CurrencyNewValueOnForm").ToString <> "" Then
                // iCurr = goP.GetVar("OP_CurrencyNewValueOnForm")
                // Else
                // iCurr = doRS.GetFieldVal("MLS_CURRENCY", 2)
                // End If

                switch (Strings.UCase(par_s1))
                {
                    case "OP":
                        {
                            doRS.SetFieldVal("CUR_ValueEuro", 0, 2);
                            doRS.SetFieldVal("CUR_ValueIndexEuro", 0, 2);
                            doRS.SetFieldVal("CUR_ValueSterling", 0, 2);
                            doRS.SetFieldVal("CUR_ValueIndexSterling", 0, 2);
                            doRS.SetFieldVal("CUR_ValueDollar", 0, 2);
                            doRS.SetFieldVal("CUR_ValueIndexDollar", 0, 2);
                            iCurr = Convert.ToInt32(doRS.GetFieldVal("MLS_Currency", 2));

                            switch (iCurr)
                            {
                                case 0: // <Select>
                                    {
                                        break;
                                    }

                                case 1: // US Dollar to Euro
                                    {
                                        cExchRate = Convert.ToDecimal(goTR.StrRead(sWOP, "ExchRate_DollarToEuro")); // US Dollar to Euro

                                        // Copy value to Dollar fields
                                        doRS.SetFieldVal("CUR_ValueDollar", cValue, 1);
                                        doRS.SetFieldVal("CUR_ValueIndexDollar", cValue * Convert.ToDecimal(rProb), 1);

                                        // Convert Dollar to Euro
                                        doRS.SetFieldVal("CUR_ValueEuro", cValue * cExchRate, 1);

                                        // Calculate ValueIndexEuro
                                        doRS.SetFieldVal("CUR_ValueIndexEuro", cValue * Convert.ToDecimal(rProb) * cExchRate, 1);

                                        doRS.SetFieldVal("TXT_CurrSymbol", "$");
                                        break;
                                    }

                                case 2: // Sterling to Euro
                                    {
                                        // Case 1 'Sterling to Euro 
                                        // VS 01302015 : Bug Changed from 1 to 2

                                        cExchRate = Convert.ToDecimal(goTR.StrRead(sWOP, "ExchRate_SterlingToEuro")); // Sterling to Euro

                                        // Copy value to Sterling fields
                                        doRS.SetFieldVal("CUR_ValueSterling", cValue, 1);
                                        doRS.SetFieldVal("CUR_ValueIndexSterling", cValue * Convert.ToDecimal(rProb), 1);

                                        // Convert Sterling to Euro
                                        doRS.SetFieldVal("CUR_ValueEuro", cValue * cExchRate, 1);

                                        // Calculate ValueIndexEuro
                                        doRS.SetFieldVal("CUR_ValueIndexEuro", cValue * Convert.ToDecimal(rProb) * cExchRate, 1);

                                        doRS.SetFieldVal("TXT_CurrSymbol", Strings.Chr(163));
                                        break;
                                    }

                                case 3: // Euro
                                    {
                                        // VS 01302015 TKT#293 : Added Euro to the List No Conversion Neccesary

                                        // Copy value to Euro fields
                                        doRS.SetFieldVal("CUR_ValueEuro", cValue, 1);
                                        doRS.SetFieldVal("CUR_ValueIndexEuro", cValue * Convert.ToDecimal(rProb), 1);

                                        doRS.SetFieldVal("TXT_CurrSymbol", Strings.Chr(128));
                                        break;
                                    }
                            }

                            break;
                        }
                }
            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/19/2014 Disable Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/19/2014 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                par_doCallingObject = doForm;
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/19/2014 Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")) == Convert.ToString(doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID")))
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CN", "MergeFail");
                        else
                            doForm.MessageBox("This record will be merged to the target record, '" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name")) + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");
                    }
                }
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // -------Target Account
            // TLD 7/24/2012 Disable MMO_TargetAcctHistory & DTT_LASTTARGETACCTCHANGE
            // Written to by system when CHK_TargetAcct is checked/unchecked
            doForm.SetControlState("MMO_TargetAcctHistory", 1); // Lock, but allow scrolling
            doForm.SetControlState("DTE_LastTargetAcctChange", 4); // Gray
            doForm.SetControlState("DTE_CustModTime", 4); // Gray
            doForm.SetControlState("TME_CustModTime", 4); // Gray
            doForm.SetControlState("TXT_CusModBy", 4); // Gray
            doForm.SetControlState("INT_CURCOUNT", 4);
            doForm.SetControlState("INT_POTCOUNT", 4);
            doForm.SetControlState("SR__POTENTIALPERC", 4);
            doForm.SetControlState("SR__POTENTIALPORTFOLIO", 4);
            doForm.SetControlState("SR__PRODLINEPOT", 4);
            doForm.SetControlState("TXT_CURRANDPOT", 4);
            doForm.SetControlState("TXT_PRODPOTQUAD", 4);

            // TLD 6/19/2014 Disable Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);

            // Track CHK_TargetAcct status on open
            // for us in recording date checked and history
            doForm.oVar.SetVar("CO_TargetAccount", doForm.doRS.GetFieldVal("CHK_TargetAcct"));
            // -------End Target Account

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/19/2014 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sOldTargetAcct = Convert.ToString(doForm.oVar.GetVar("CO_TargetAccount"));
            string sNewTargetAcct = Convert.ToString(doForm.doRS.GetFieldVal("CHK_TargetAcct"));

            // ----------Target Account
            // TLD 7/24/2012 If CHK_TargetAcct was unchecked on load
            // and checked now, write to DTT_LASTTARGETACCTCHANGE
            // write to MMO_TargetAcctHistory ANY change
            if (Convert.ToString(doForm.oVar.GetVar("CO_TargetAccount_Ran")) != "1")
            {
                if (sOldTargetAcct != sNewTargetAcct)
                {
                    string sWork = "";
                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("MMO_TargetAcctHistory"));
                    if (sNewTargetAcct == "Checked")
                    {
                        sWork = goTR.WriteLogLine(sWork, " Checked.");
                        doForm.doRS.SetFieldVal("DTT_LastTargetAcctChange", "Today|Now");
                    }
                    else
                        // unchecked
                        sWork = goTR.WriteLogLine(sWork, " Unhecked.");
                    doForm.oVar.SetVar("CO_TargetAccount_Ran", "1");
                    doForm.doRS.SetFieldVal("MMO_TargetAcctHistory", sWork);
                }
            }
            // ----------End Target Account

            // TLD 6/19/2014 Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")) == Convert.ToString(doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID")))
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CO", "MergeFail");
                        else
                            doForm.MessageBox("This record will be merged to the target record, '" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name")) + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CO", "Merge");
                    }
                }
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sCurVol = "";
            string sPotVol = "";
            int iCurCount = Convert.ToInt32( doRS.GetLinkCount("LNK_Current_PD"));
            int iPotCount = Convert.ToInt32(doRS.GetLinkCount("LNK_Potential_PD"));


            // ---------TLD 7/24/2012 Target Account Matrix Profiling
            // Copy LNK_Current_PD to LNK_Potential_PD
            doRS.SetFieldVal("LNK_Potential_PD", doRS.GetFieldVal("LNK_Current_PD"));

            // Record total selections from LNK_Current_PD and LNK_Potential_PD
            doRS.SetFieldVal("INT_CURCOUNT", iCurCount, 2);
            doRS.SetFieldVal("INT_POTCOUNT", iPotCount, 2);

            // Calculate Potential Percentage & Potential Portfolio
            clRowSet doPDRS = new clRowSet("PD", 3, null, null, "GID_ID");
            int iPDCount = Convert.ToInt32(doPDRS.Count());
            if (iPDCount != 0)
            {
                if (iPotCount != 0)
                    doRS.SetFieldVal("SR__POTENTIALPERC", (iCurCount / (double)iPotCount) * 100, 2);
                doRS.SetFieldVal("SR__POTENTIALPORTFOLIO", (iPotCount / (double)iPDCount) * 100, 2);

                doRS.SetFieldVal("SR__ProdLinePot", (iPotCount - iCurCount), 2);
                sCurVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_CURVOLUME")), 1);
                sPotVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_POTVOLUME")), 1);
                if (sCurVol == "<")
                    sCurVol = "Z";
                if (sPotVol == "<")
                    sPotVol = "Z";

                // set field to cur & pot
                doRS.SetFieldVal("TXT_CURANDPOT", sCurVol + sPotVol);
            }

            // Set Product Potential Quadrant
            double rTotalPortfolio = Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2));
            double rPotentialProduct = Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPERC", 2));

            if (rTotalPortfolio >= 51 & rTotalPortfolio <= 100)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                    // Set to 1
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "1");

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                    // Set to 3
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "3");
            }

            if (rTotalPortfolio >= 0 & rTotalPortfolio <= 50)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                    // Set to 2
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "2");

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                    // Set to 4
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "4");
            }

            // Because COs are updated nightly to set custom
            // date fields, need to write to custom mod time and mod by fields
            // AutoCOUpdate does NOT run recordonsave
            doRS.SetFieldVal("TXT_CusModBy", goP.GetMe("CODE"));
            doRS.SetFieldVal("DTT_CusModTime", "Today|Now");
            // ---------TLD 7/24/2012 End Target Account Matrix Profiling

            par_doCallingObject = doRS;
            return true;
        }
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }
        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/19/2014 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + Convert.ToString(doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName())) + "'", "", "**", -1, "", "", "", "", "", true, true, false, false, -1, "", true);
                if (doRSMergeTo.GetFirst()== 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    else
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + Convert.ToString(doRSMerge.GetFieldVal("SYS_Name")) + " ==" + Constants.vbCrLf + Convert.ToString(doRSMerge.GetFieldVal(sField)));
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | int.Parse(sLinkType[1]) == 2)
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (Convert.ToString(doRSMergeTo.GetFieldVal(aLinks.GetItem(i))) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    // Check Active if exists
                    if (goData.IsFieldValid(doRSMerge.GetFileName(), "CHK_ACTIVEFIELD") == true)
                        doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);

                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;

            par_doCallingObject = doRSMerge;
            return true;
        }
        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJournal = "";
            string sWork = "";
            //DateTime dtNow = goTR.NowLocal();

            switch (Strings.UCase(par_s5))
            {
                case "MERGE":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    //goScr.RunScript("MergeRecord", doForm.doRS);
                                    par_doCallingObject = doForm.doRS;
                                    scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                    doForm.doRS = (clRowSet)par_doCallingObject;
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }
                case "OP_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                    {
                        // Select Case UCase(par_s1)
                        // Case "OK"
                        // sJournal = doForm.doRS.GetFieldVal("MMO_Journal")
                        // 'CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                        // 'Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                        // sWork = par_s2
                        // doForm.ovar.setvar("JournalWithHardReturns", sWork & vbCrLf & sJournal)
                        // If sWork <> "" Then
                        // 'CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                        // 'If not defined in WOP, default is 1
                        // If doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS") <> "0" Then
                        // sWork = goTR.Replace(sWork, vbCrLf, Chr(32) & Chr(32) & Chr(32))
                        // doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork & vbCrLf & sJournal)
                        // Else
                        // doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork & vbCrLf & sJournal)
                        // End If
                        // doForm.MoveToField("MMO_JOURNAL")
                        // 'doForm.oVar.SetVar("sJournalVal", sWork)
                        // End If
                        // End Select
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMR_Journal"));
                                    // CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                    // Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                    sWork = par_s2;

                                    string sresult = "";
                                    string sfinal = "";
                                    string sDateStamp = null;

                                    string ss = sWork.Remove(0, 16);

                                    doForm.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                    if (sWork != "")
                                    {
                                        // CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                        // If not defined in WOP, default is 1
                                        if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS")) != "0")
                                        {
                                            // sWork = goTR.Replace(sWork, vbCrLf, Chr(32) & Chr(32) & Chr(32))
                                            // doForm.doRS.SetFieldVal("MMR_JOURNAL", sWork & vbCrLf & sJournal)
                                            if (sJournal == "")
                                            {
                                                StringBuilder sb = new StringBuilder();
                                                DateTime dtNow = goTR.NowLocal();
                                                dtNow = goTR.NowServer();
                                                sDateStamp = goTR.DateToString(dtNow,"", ref par_iValid) + " " + goTR.TimeToString(dtNow,"", ref par_iValid);


                                                sb.Append("<table cellpadding='5' cellspacing='0' style='width:100%border: 1px solid #ccc;font-size: 9pt;font-family:Arial'>");
                                                sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Date</th>"));
                                                sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Journal</th>"));
                                                sb.Append("<tr>");
                                                sb.Append(("<td style='width:20%;border: 1px solid #ccc'>" + sDateStamp + "</td>"));
                                                sb.Append(("<td style='width:80%px;border: 1px solid #ccc'>" + ss + "</td>"));
                                                sb.Append("</tr>");
                                                sb.Append("</table>");

                                                sWork = sb.ToString();
                                                doForm.doRS.SetFieldVal("MMR_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            }
                                            else if (sJournal.Contains("</th>"))
                                            {
                                                sresult = sJournal;
                                                string[] sr = sresult.Split(new string[] { "</th>" }, StringSplitOptions.None);
                                                sfinal = sr[2];
                                                StringBuilder sb = new StringBuilder();
                                                DateTime dtNow;
                                                dtNow = goTR.NowServer();
                                                sDateStamp = goTR.DateToString(dtNow,"", ref par_iValid) + " " + goTR.TimeToString(dtNow,"", ref par_iValid);

                                                sb.Append("<table cellpadding='5' cellspacing='0' style='width:100%;border: 1px solid #ccc;font-size: 9pt;font-family:Arial'>");
                                                sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Date</th>"));
                                                sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Journal</th>"));
                                                sb.Append("<tr>");
                                                sb.Append(("<td style='width:10%;border: 1px solid #ccc'>" + sDateStamp + "</td>"));
                                                sb.Append(("<td style='width:90%;border: 1px solid #ccc'>" + ss + "</td>"));
                                                sb.Append("</tr>");
                                                sb.Append(sfinal);

                                                sWork = sb.ToString();
                                                doForm.doRS.SetFieldVal("MMR_JOURNAL", sWork);
                                            }
                                            else
                                            {
                                                StringBuilder sb = new StringBuilder();
                                                DateTime dtNow;
                                                dtNow = goTR.NowServer();
                                                sDateStamp = goTR.DateToString(dtNow,"", ref par_iValid) + " " + goTR.TimeToString(dtNow,"", ref par_iValid);
                                                sb.Append("<table cellpadding='5' cellspacing='0' style='width:100%;border: 1px solid #ccc;font-size: 9pt;font-family:Arial'>");
                                                sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Date</th>"));
                                                sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Journal</th>"));
                                                sb.Append("<tr>");
                                                sb.Append(("<td style='width:10%;border: 1px solid #ccc'>" + sDateStamp + "</td>"));
                                                sb.Append(("<td style='width:90%;border: 1px solid #ccc'>" + ss + "</td>"));
                                                sb.Append("</tr>");
                                                sb.Append("</table>");

                                                sWork = sb.ToString();
                                                doForm.doRS.SetFieldVal("MMR_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            }
                                        }
                                        else
                                            // doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork & vbCrLf & sJournal)
                                            if (sJournal == "")
                                        {
                                            StringBuilder sb = new StringBuilder();
                                            DateTime dtNow;
                                            dtNow = goTR.NowServer();
                                            sDateStamp = goTR.DateToString(dtNow, "", ref par_iValid) + " " + goTR.TimeToString(dtNow, "", ref par_iValid);
                                            sb.Append("<div style='overflow:auto'>");
                                            sb.Append("<table cellpadding='5' cellspacing='0' style='width:100%border: 1px solid #ccc;font-size: 9pt;font-family:Arial'>");
                                            sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Date</th>"));
                                            sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Journal</th>"));
                                            sb.Append("<tr>");
                                            sb.Append(("<td style='width:10%;border: 1px solid #ccc'>" + sDateStamp + "</td>"));
                                            sb.Append(("<td style='width:90%px;border: 1px solid #ccc'>" + ss + "</td>"));
                                            sb.Append("</tr>");
                                            sb.Append("</table>");
                                            sb.Append("</div>");

                                            sWork = sb.ToString();
                                            doForm.doRS.SetFieldVal("MMR_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                        }
                                        else if (sJournal.Contains("</th>"))
                                        {
                                            sresult = sJournal;
                                            string[] sr = sresult.Split(new string[] { "</th>" }, StringSplitOptions.None);
                                            sfinal = sr[2];
                                            StringBuilder sb = new StringBuilder();
                                            DateTime dtNow;
                                            dtNow = goTR.NowServer();
                                            sDateStamp = goTR.DateToString(dtNow, "", ref par_iValid) + " " + goTR.TimeToString(dtNow, "", ref par_iValid);
                                            sb.Append("<div style='overflow:auto'>");
                                            sb.Append("<table cellpadding='5' cellspacing='0' style='width:100%;border: 1px solid #ccc;font-size: 9pt;font-family:Arial'>");
                                            sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Date</th>"));
                                            sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Journal</th>"));
                                            sb.Append("<tr>");
                                            sb.Append(("<td style='width:10%;border: 1px solid #ccc'>" + sDateStamp + "</td>"));
                                            sb.Append(("<td style='width:90%;border: 1px solid #ccc'>" + ss + "</td>"));
                                            sb.Append("</tr>");
                                            sb.Append(sfinal);

                                            sWork = sb.ToString();
                                            doForm.doRS.SetFieldVal("MMR_JOURNAL", sWork);
                                        }
                                        else
                                        {
                                            StringBuilder sb = new StringBuilder();
                                            DateTime dtNow;
                                            dtNow = goTR.NowServer();
                                            sDateStamp = goTR.DateToString(dtNow, "", ref par_iValid) + " " + goTR.TimeToString(dtNow, "", ref par_iValid);
                                            sb.Append("<div style='overflow:auto'>");
                                            sb.Append("<table cellpadding='5' cellspacing='0' style='width:100%;border: 1px solid #ccc;font-size: 9pt;font-family:Arial'>");
                                            sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Date</th>"));
                                            sb.Append(("<th style='background-color: #B8DBFD;border: 1px solid #ccc'>Journal</th>"));
                                            sb.Append("<tr>");
                                            sb.Append(("<td style='width:10%;border: 1px solid #ccc'>" + sDateStamp + "</td>"));
                                            sb.Append(("<td style='width:90%;border: 1px solid #ccc'>" + ss + "</td>"));
                                            sb.Append("</tr>");
                                            sb.Append("</table>");
                                            sb.Append("</div>");

                                            sWork = sb.ToString();
                                            doForm.doRS.SetFieldVal("MMR_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                        }
                                    }

                                    break;
                                }
                        }

                        break;
                    }

                case "OP_FORMCONTROLONCHANGE_BTN_INSERTLINEMMR":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMR_Journal"));
                                    doForm.doRS.SetFieldVal("MMR_Journal", par_s2 + "<br/>" + sJournal);
                                    doForm.MoveToField("MMR_Journal");
                                    break;
                                }
                        }

                        break;
                    }
            }
            par_bRunNext=false;
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sTNo = "";

            // TLD 6/23/2014 Generate T #
            doForm.SetControlState("TXT_TNumber", 4);
            if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_TNumber")) == "")
            {
                //if (!goScr.RunScript("OPP_GenerateTNo", null, null, null, null, null, null, null, sTNo))
                //    return false;
                object oDoObject = null;
                par_oReturn = sTNo;
                if (!scriptManager.RunScript("OPP_GenerateTNo", ref oDoObject, ref par_oReturn, ref par_bRunNext, ref par_sSections))
                {
                    return false;
                }
                sTNo = par_oReturn.ToString();
                doForm.doRS.SetFieldVal("TXT_TNumber", sTNo);
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 2/12/2014 Enforce TXT_QuoteNo when Stage is Quote
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Stage", 2)) == 30 & Convert.ToString(doForm.doRS.GetFieldVal("TXT_QuoteNo")) == "")
            {
                doForm.MoveToField("TXT_QuoteNo");
                goErr.SetWarning(30029, sProc, "", Convert.ToString(doForm.GetFieldLabel("TXT_QuoteNo")), "", "", "", "", "", "", "", "", "TXT_QuoteNo");
                par_doCallingObject = doForm;
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            long lStatusVal;
            bool bResult = false;

            lStatusVal = Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
            // SKO 04/27/2016  Ticket#1058  When status is selected to no bid to auto fill the date closed
            switch (lStatusVal)
            {
                case 8: // US_8=No Bid
                    {
                        if (!goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1))) | Convert.ToString(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1)) == "")
                            doForm.doRS.SetFieldVal("DTE_DATECLOSED", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        break;
                    }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            goTR.StrWrite(ref par_sSections, "ConnectTerritoryFromRelatedCompany", "0");
            clRowSet doRS = (clRowSet)par_doCallingObject;
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
               

                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
               
            }
            par_doCallingObject = doRS;
            par_bRunNext = false;
            return true;
        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sProb = Convert.ToString(doRS.GetFieldVal("SI__Probability"));

            // TLD 8/4/2014 Calc currencies
            //goScr.RunScript("Auto_ConvertCurrencies", doRS, null, "OP", "CUR_Value", null, sProb);

            scriptManager.RunScript("Auto_ConvertCurrencies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "OP", "CUR_Value", "", sProb);

            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));         

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                
            }
            par_doCallingObject = doForm;
            par_bRunNext = false;
            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_GenerateTNo_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 10/1/07 Changed now to goTR.NowUTC().
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/23/2014 Added
            par_bRunNext = false;

            // PURPOSE:
            // TLD 6/23/2014 generate T # - sequential number stored in OTH_OPTNUMBERPOINTERS
            // RETURNS:
            // T number via return parameter.

            string s = "";

            try
            {
                string sSeq = ""; // TXT_TNumber
                string sTNoWOP = goMeta.PageRead("GLOBAL", "OTH_OPTNUMBERPOINTERS", "", false, "XX"); // WOP for OPP T #
                int iTNo = 0; // T Sequential #

                // Read WOP seqeuntial #
                sSeq = goTR.StrRead(sTNoWOP, "TNO", "", false);
                if (sSeq == "")
                {
                    // Try to find the latest T #?
                    clRowSet doOPP = new clRowSet("OP", 3, "TXT_TNumber<>''", "TXT_TNumber ASC", "TXT_TNumber", 1);
                    if (doOPP.GetFirst() == 1)
                    {
                        sSeq = Convert.ToString(doOPP.GetFieldVal("TXT_TNumber"));
                        // If number, add 1 to existing
                        par_iValid = 4;
                        if (goTR.IsNumber(sSeq))
                            iTNo = Convert.ToInt32(goTR.StringToNum(sSeq, "", ref par_iValid)) + 1;
                    }
                    else
                        // no quotes with dashes?
                        iTNo = 1;
                }
                else
                    // Not blank, is number
                    if (goTR.IsNumber(sSeq))
                    iTNo = Convert.ToInt32(goTR.StringToNum(sSeq, "", ref par_iValid));

                // 'Dont' know what to do here?
                // If iTNo = 0 Then
                // iTNo = 1
                // End If

                s = iTNo.ToString();

                // VS 02622015 TKT#304 : Change 1st 2 digits of TNumber if the year has changed       .Eg:156052 ->New Year-> 160001 
                // add another digit to TNo if it has reached next thousand before end of the year    .Eg:159999 ->Next Number-> 1500001
                // 2015 will have a prefix of 156
                string sTNo = goTR.NumToString(iTNo);
                string sTNoYrPrefix;
                string sTwoDigitYear = Strings.Mid(goTR.NowUTC().Year.ToString(), 3);   // Get Current Year 
                par_iValid = 4;
                int iTwoDigitYear = Convert.ToInt32(goTR.StringToNum(sTwoDigitYear, "", ref par_iValid));
                string sNewTNo = "";
                int iOPNum = 0;
                string sOPNum = "";

                // Special Case for 2015 use 156 as prefix For the rest only use 2digit year(16,17,18,...)
                if (iTwoDigitYear == 15)
                {

                    // Get 3 Digit Prefix(156) for 2015
                    if (goTR.NumToString(sTNo).Length > 3)
                    {
                        sOPNum = Strings.Mid(sTNo, 4);
                        iOPNum = Convert.ToInt32(goTR.StringToNum(sOPNum, "", ref par_iValid));
                    }

                    // Check if incrementing iTNo will change Year Prefix and add another Digit
                    if (sOPNum.Length < goTR.NumToString((iOPNum + 1)).Length)
                    {
                        sTNo = Strings.Mid(sTNo, 1, 3) + goTR.Pad("0", (sOPNum.Length + 1), "0", "L");   // Add 0..01 to Year Digits
                        iTNo = Convert.ToInt32(goTR.StringToNum(sTNo, "", ref par_iValid));
                    }
                }
                else
                {

                    // Get digits after Year prefix
                    if (goTR.NumToString(sTNo).Length > 2)
                    {
                        sOPNum = Strings.Mid(sTNo, 3);
                        iOPNum = Convert.ToInt32(goTR.StringToNum(sOPNum, "", ref par_iValid));
                    }

                    // Check if incrementing iTNo will change Year Prefix and add another Digit
                    if (sOPNum.Length < goTR.NumToString((iOPNum + 1)).Length)
                    {
                        sTNo = Strings.Mid(sTNo, 1, 2) + goTR.Pad("0", (sOPNum.Length + 1), "0", "L");   // Add 0..01 to Year Digits
                        iTNo = Convert.ToInt32(goTR.StringToNum(sTNo, "", ref par_iValid));
                    }
                }

                // Change prefix if year has changed and start fresh numbering
                if (sTNo.Length >= 2)
                {
                    sTNoYrPrefix = Strings.Mid(sTNo, 1, 2);  // Get Year in TNo
                    if (Convert.ToInt32(goTR.StringToNum(sTNoYrPrefix, "", ref par_iValid)) < iTwoDigitYear)
                    {
                        // Start New Numbering from YY0001
                        sNewTNo = sTwoDigitYear + "0000";
                        iTNo = Convert.ToInt32(goTR.StringToNum(sNewTNo, "", ref par_iValid));
                    }
                }

                // Write Next Sequential # to WOP
                goMeta.LineWrite("GLOBAL", "OTH_OPTNUMBERPOINTERS", "TNO", iTNo + 1, ref par_oConnection, "", "XX");
            }

            catch (Exception ex)
            {
                goLog.Log(sProc, "failed with error " + goErr.GetLastError());
            }

            par_oReturn = s;

            return true;
        }

        public bool OP_FormControlOnChange_BTN_INSERTLINEMMR_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // Dim sWork As String
            string sParams = "";

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName: '" & par_sFieldName & "'", "", sProc)

            scriptManager.RunScript("GetDateTimeStamp",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections, null, "NEUTRAL", "sDateStamp", "CODE", "USERNOOFFSETLABEL"); // returns var sDateStamp

            // goTR.StrWrite(sParams, "TITLE", "Add Note")
            // CS:Inputbox must be replaced in web context.
            // sWork = InputBox(doForm.oVar.GetVar("sDateStamp") & " ", sParams, doForm)
            // next line wt mod for release

            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", null, null, doForm.oVar.GetVar("sDateStamp") + " ", "MessageBoxEvent", null, null, null, null, "OK", null, null, null, System.Reflection.MethodInfo.GetCurrentMethod().Name, null, true);
            par_doCallingObject = doForm;
            return true;



            // sWork = doForm.oVar.GetVar("sDateStamp")
            // If sWork <> "" Then
            // sWork &= vbCrLf & vbCrLf & doForm.doRS.GetFieldVal("MMO_JOURNAL")
            // doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork)
            // doForm.MoveToField("MMO_JOURNAL")
            // 'doForm.oVar.SetVar("sJournalVal", sWork)
            // End If

            return true;
        }

        public bool OP_FormControlOnChange_BTN_INSERTLINE_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // Dim sWork As String
            string sParams = "";

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName: '" & par_sFieldName & "'", "", sProc)

            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "sDateStamp", "CODE", "USERNOOFFSETLABEL"); // returns var sDateStamp

            // goTR.StrWrite(sParams, "TITLE", "Add Note")
            // CS:Inputbox must be replaced in web context.
            // sWork = InputBox(doForm.oVar.GetVar("sDateStamp") & " ", sParams, doForm)
            // next line wt mod for release

            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", null, null, doForm.oVar.GetVar("sDateStamp") + " ", "MessageBoxEvent", null, null, null, null, "OK", null, null, null, System.Reflection.MethodInfo.GetCurrentMethod().Name);
            par_doCallingObject = doForm;
            return true;



            // sWork = doForm.oVar.GetVar("sDateStamp")
            // If sWork <> "" Then
            // sWork &= vbCrLf & vbCrLf & doForm.doRS.GetFieldVal("MMO_JOURNAL")
            // doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork)
            // doForm.MoveToField("MMO_JOURNAL")
            // 'doForm.oVar.SetVar("sJournalVal", sWork)
            // End If

            return true;
        }


        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            return true;

        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quote_FillAddress_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //goP.TraceLine("", "", sProc)

            //PURPOSE:
            //		Fill the address field, first checking whether it is empty.
            //RETURNS:
            //		True

            string sContactName = "";
            string sMailingAddr = null;
            string sFirstName = null;
            string sLastName = null;
            //Dim sCompName as string
            string sAddrMail = null;
            string sCityMail = null;
            string sStateMail = null;
            string sZipMail = null;
            string sCountryMail = null;
            string scompany = null;

            //VS 03262018 TKT#2151 : Refill data whenever Contact has changed even if not empty.
            //if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING").ToString()))
            //    return true;
            if (doForm.doRS.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1)
                return true;

            //CS 6/22/09: Create CN rowset to get CN fields
            clRowSet doRSContact = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN") + "'", "", "LNK_RELATED_CO,TXT_NAMEFIRST,TXT_NAMELAST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
            if (doRSContact.GetFirst() == 1)
            {
                scompany = Convert.ToString(doRSContact.GetFieldVal("LNK_RELATED_CO%%TXT_COMPANYNAME"));
                sFirstName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMEFIRST"));
                sLastName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMELAST"));


                if (!string.IsNullOrEmpty(sFirstName))
                {
                    sContactName = sFirstName + " ";
                }
                sContactName += sLastName;

                sAddrMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ADDRBUSINESS"));
                sCityMail = Convert.ToString(doRSContact.GetFieldVal("TXT_CITYBUSINESS"));
                sStateMail = Convert.ToString(doRSContact.GetFieldVal("TXT_STATEBUSINESS"));
                sZipMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ZIPBUSINESS"));
                sCountryMail = Convert.ToString(doRSContact.GetFieldVal("TXT_COUNTRYBUSINESS"));

                //Start building the mailing address
                //sMailingAddr = scompany;
                //if (!string.IsNullOrEmpty(scompany))
                //{
                //    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                //}
                //sMailingAddr = scompany;
                sMailingAddr = sContactName;
                if (!sAddrMail.Contains(scompany))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                }
                if (!string.IsNullOrEmpty(sAddrMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sAddrMail;
                }
                if (!string.IsNullOrEmpty(sCityMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCityMail;
                }
                if (!string.IsNullOrEmpty(sStateMail))
                {
                    sMailingAddr = sMailingAddr + ", " + sStateMail;
                }
                if (!string.IsNullOrEmpty(sZipMail))
                {
                    sMailingAddr = sMailingAddr + " " + sZipMail;
                }
                if (!string.IsNullOrEmpty(sCountryMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCountryMail;
                }
                doForm.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailingAddr);
                doForm.doRS.SetFieldVal("MMO_ADDRMAILING", sMailingAddr);
            }


            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;

        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Utility_RunImportUtility_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/19/2014
            par_bRunNext = false;

            try
            {
                goUI.OpenURLExternal("../Pages/cus_diaImportMan.aspx", "Selltis", "height=840,width=1250,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool XW_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");

            // --------------------Exchange Rates
            // TLD 8/4/2014 Sterling, Dollar to Euro
            doForm.doRS.SetFieldVal("SR__SterlingToEuro", goTR.StrRead(sWOP, "ExchRate_SterlingToEuro"));
            doForm.doRS.SetFieldVal("SR__DollarToEuro", goTR.StrRead(sWOP, "ExchRate_DollarToEuro"));

            par_doCallingObject = doForm;
            return true;
        }
        public bool XW_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 8/4/2014 Custom Workgroup options
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");

            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                par_doCallingObject = doForm;
                return false;
            }

            // -------------------------Exchange Rates
            // Write values to MD
            goTR.StrWrite(ref sWOP, "ExchRate_SterlingToEuro", Convert.ToString(doForm.doRS.GetFieldVal("SR__SterlingToEuro")));
            goTR.StrWrite(ref sWOP, "ExchRate_DollarToEuro", Convert.ToString(doForm.doRS.GetFieldVal("SR__DollarToEuro")));

            goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, "", "", "XX");

            par_doCallingObject = doForm;
            return true;
        }

        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            //string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));



            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_FOR_MO,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            //rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //mobile Ol line number
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    iLineCount = doOPLines.Count();
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }

        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                //doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);


            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", true);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "N-Sea_StandardQuote_draft.docx";
                }

            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "N-Sea_StandardQuote.docx";
                }

            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }
            if (doForm.Save(3) != 1)
            {
                goLog.SetErrorMsg("Save failed for QT PDF Generation ");
                //return false;
            }
            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                       
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }


        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }
        public bool AutoQuoteDuplicate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //MI 4/25/07 CREATED BY MI 4/22/07
            //PURPOSE:
            //       Duplicate an existing Quote and its line items allowing the user to connect a different
            //       Contact, Company, etc.

            string sID = null;
            clRowSet doRowset = default(clRowSet);
            string sFileName = null;
            Form doF = default(Form);
            string sOrigQuoteName = null;
            string sOrigQuoteID = null;

            //Check selected record
            //goUI - Not Implemented
            //sID = HttpContext.Current.Session["SelectedRecordID"].ToString(); //goUI.GetLastSelected("SELECTEDRECORDID");

            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
                sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();

            //goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
            if (sFileName != "QT")
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            //Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("You cannot duplicate the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }


            //Copy the selected record
            //Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", "", "*,LNK_CONNECTED_QL%%SYS_Name", 1);
            if (doRowset.Count() < 1)
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }
            else
            {
                sOrigQuoteName = doRowset.GetFieldVal("SYS_Name").ToString();
                sOrigQuoteID = doRowset.GetFieldVal("GID_ID").ToString();
            }

            //Create the new Quote form
            doF = new Form(sFileName, "", "CRU_" + sFileName);
            doF.oVar.SetVar("QuoteOpeningMode", "Duplicate");
            doF.oVar.SetVar("QuoteOrinalQuoteID", sID);
            //doF.SetControlVal("NDB_MMO_Lines", doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name").ToString());
            doF.doRS.SetFieldVal("TXT_Description", doRowset.GetFieldVal("TXT_Description", 2), 2);
            doF.doRS.SetFieldVal("CUR_Subtotal", doRowset.GetFieldVal("CUR_Subtotal", 2), 2);
            doF.doRS.SetFieldVal("CUR_Total", doRowset.GetFieldVal("CUR_Total", 2), 2);
            //Set History tab & Cloned from Quote
            doF.doRS.SetFieldVal("MMO_History", "Duplicated from Quote" + sOrigQuoteName);
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sOrigQuoteID);
            doF.doRS.SetFieldVal("LNK_CONNECTED_QL", doRowset.GetFieldVal("LNK_CONNECTED_QL", 2), 2);

            doF.MessagePanel("This is a duplicate of the Quote '" + sOrigQuoteName + "'." + Environment.NewLine + "Fill out the form and click Save or click Modify Lines to add, edit or remove them.", "#FFFFB0", "#000000", "Info.gif");

            //'Copy to the new rowset
            //If Not goData.CopyRecord(doRowset, doF.doRS) Then
            //    goErr.SetError(35000, sProc, "Copying the selected Quote '" & sID & "' failed.")
            //    Return False
            //End If
            //doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            //doF.doRS.ClearLinkAll("LNK_OriginatedBy_CN")
            //doF.doRS.ClearLinkAll("LNK_To_CO")

            //'Save the new record (?)
            //If doNewRowset.Commit() = 0 Then
            //    goErr.SetWarning(30200, sProc, "", "An error occurred duplicating the selected Quote.", "", "", "", "", "", "", "", "", "")
            //    doRowset = Nothing
            //    doNewRowset = Nothing
            //    Return False
            //End If

            goUI.Queue("FORM", doF);

            //Clean up objects
            doRowset = null;
            par_bRunNext = false;
            par_doCallingObject = doF;
            return true;
        }

        public bool AutoQuoteCreateRevision_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sID = null;
            clRowSet doRowset = default(clRowSet);
            string sFileName = null;
            Form doF = default(Form);
            clRowSet doAllQTs = default(clRowSet);
            string sQuoteNo = null;
            string sQuoteOnlyNo = null;
            //Quote number without the revision
            int iRevNo = 0;
            int iRevNoTemp = 0;
            int iLen = 0;
            int i = 0;
            int iPos = 0;
            string sQuoteTitleOnly = null;

            //Check selected record
            //goUI - Not Implemented
            //sID = goUI.GetLastSelected("SELECTEDRECORDID");

            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
                sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();

            Desktop doDesktop = (Desktop)par_doCallingObject;

            //string sessionUserId = HttpContext.Current.Session["SelectedRecordID"] as string;

            if (!string.IsNullOrEmpty(sID))
            {
                //sID = HttpContext.Current.Session["SelectedRecordID"].ToString();

                //goP.TraceLine("sID: " & sID & "'", "", sProc)
                sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
                if (sFileName != "QT")
                {
                    //goUI - Not Implemented
                    goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null);
                    //doDesktop.MessageBox(ref par_doCallingObject, "Please select a Quote first.");
                    return true;
                }

            }
            else
            {
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }


            //Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("You cannot create a revision of the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            //Copy the selected record
            //Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            //------------------- Update revision no in Quote No -------------------
            sQuoteNo = Strings.Trim(doRowset.GetFieldVal("TXT_QuoteNo").ToString());
            if (string.IsNullOrEmpty(sQuoteNo))
            {
                object callingObject = null;
                if (!scriptManager.RunScript("Quote_GenerateQuoteNo", ref callingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", sQuoteNo))
                {
                    return false;
                }
                else
                {
                    sQuoteNo = par_oReturn.ToString();
                }
            }
            if (string.IsNullOrEmpty(sQuoteNo))  //sQuoteNo
            {
                //Should never happen
                goErr.SetError(35000, sProc, "Quote number is blank.");
                return false;
            }

            sQuoteOnlyNo = sQuoteNo;// sQuoteNo;
            //Get the quote no without the revision number
            iPos = Strings.InStr(sQuoteOnlyNo, "-");
            if (iPos > 0)
            {
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, iPos - 1);
            }
            //Limit the length of the quote no just in case someone mangled it by hand
            iLen = Strings.Len(sQuoteOnlyNo);
            if (iLen > 14)
            {
                iLen = 14;
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, 14);
            }

            //Determine the highest revision no
            doAllQTs = new clRowSet(sFileName, clC.SELL_READONLY, "TXT_QuoteNo[='" + goTR.PrepareForSQL(sQuoteOnlyNo) + "'", "DTT_Time DESC", "TXT_QuoteNo");
            //MI 11/6/08 Added goTr.PrepareForSQL
            iRevNo = 0;
            //*** MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            for (i = 1; i <= doAllQTs.Count(); i++)
            {
                sQuoteNo = doAllQTs.GetFieldVal("TXT_QuoteNO").ToString();
                iPos = Strings.InStr(sQuoteNo, "-");
                if (iPos > 0)
                {
                    //The number contains a revision no
                    int par_iValid = 4;
                    iRevNoTemp = Convert.ToInt32(goTR.StringToNum(Strings.Right(sQuoteNo, 3), "", ref par_iValid));
                    if (iRevNoTemp > iRevNo)
                    {
                        iRevNo = iRevNoTemp;
                    }
                }
                if (doAllQTs.GetNext() != 1)
                    break; // TODO: might not be correct. Was : Exit For
            }

            //Advance the revision number by 1, but not if revision is 1.
            iRevNo = iRevNo + 1;
            if (iRevNo > 999)
                iRevNo = 999;
            //Remove all spaces - there can be a space after the user code if shorter than 4 chars
            sQuoteNo = goTR.Replace(sQuoteOnlyNo, " ", "_");
            //'Ensure fixed length - commented because it makes numbers ugly with "_"
            //sQuoteNo = goTR.Pad(sQuoteNo, 14, "_")
            sQuoteNo = sQuoteOnlyNo + "-" + goTR.Pad(iRevNo.ToString(), 3, "0", "L");
            if ((doAllQTs != null))
                doAllQTs = null;

            //Create the new form
            doF = new Form(sFileName, "", "CRU_" + sFileName);

            //Copy this quote to the new form's rowset
            clRowSet dof_dors = doF.doRS;
            if (!goData.CopyRecord(ref doRowset, ref dof_dors))
            {
                goErr.SetError(35000, sProc, "Copying the selected Quote '" + sID + "' failed.");
                return false;
            }

            //doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            doF.doRS.SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID());
            doF.doRS.SetFieldVal("DTT_Time", "Today|Now");
            //CS 8/3/07: Reason and Status of revised QT should be same as original QT
            //doF.doRS.SetFieldVal("MLS_Status", 0, clC.SELL_SYSTEM)    'Open
            //doF.doRS.SetFieldVal("MLS_ReasonWonLost", 0, clC.SELL_SYSTEM)    '<Make selection>
            doF.doRS.SetFieldVal("DTT_DateClosed", "");
            //Set History tab & Cloned From Qt
            doF.doRS.SetFieldVal("MMO_History", "Revision of Quote" + doRowset.GetFieldVal("SYS_Name"));
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sID);
            sQuoteTitleOnly = doRowset.GetFieldVal("TXT_QuoteTitle").ToString();
            iPos = Strings.InStr(sQuoteTitleOnly, " REV ");
            if (iPos > 0)
            {
                sQuoteTitleOnly = Strings.Left(sQuoteTitleOnly, iPos - 1);
            }
            doF.doRS.SetFieldVal("TXT_QuoteTitle", sQuoteTitleOnly + " REV " + iRevNo.ToString());
            doF.doRS.SetFieldVal("TXT_QuoteNo", sQuoteNo);
            doF.doRS.ClearLinkAll("LNK_Connected_QL");
            doF.doRS.SetFieldVal("LNK_CONNECTED_QL", doRowset.GetFieldVal("LNK_CONNECTED_QL", 2), 2);
            //doF.SetControlVal("NDB_MMO_Lines", doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name").ToString());
            //Lines will display as a memo
            doF.oVar.SetVar("QuoteOpeningMode", "Revision");
            doF.oVar.SetVar("QuoteOrinalQuoteID", doRowset.GetFieldVal("GID_ID"));
            doF.MessagePanel("This is a revision of the Quote '" + doRowset.GetFieldVal("SYS_Name") + "'." + Environment.NewLine + "Check the Title and Quote Number and click Save or click Modify Lines to add, edit or remove them.", "#FFFFB0", "#000000", "Info.gif");

            goUI.Queue("FORM", doF);

            //Clean up objects
            doRowset = null;
            par_bRunNext = false;
            par_doCallingObject = doF;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_MODELNAME"));
            string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);
            rsQL.SetFieldVal("TXT_MODEL", sModelText);
            rsQL.SetFieldVal("MMO_DETAILS", sModelDesc);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }

        //public bool GenerateSysName_post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{

        //    string sProc = "clScripts:GenerateSysName";
        //    ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    clRowSet doRS = (clRowSet)par_doCallingObject;
        //    string sTemp = "";
        //    string sTemp2 = "";
        //    string sTemp3 = "";
        //    string sTemp4 = "";
        //    string sFileName = doRS.GetFileName();
        //    string sResult = "";
        //    clRowSet doLink = default(clRowSet);
        //    int iLen = 0;
        //    string par_sDelim = " ";

        //    //We assume that sFileName is valid. If this is a problem, test it here and SetError.

        //    switch (Microsoft.VisualBasic.Strings.UCase(sFileName))
        //    {

        //        case "OP":
        //            //==> OPP NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+LNK_For_Company%%TXT_CompanyName+" "+...
        //            //				LNK_For_Product%%TXT_ProductName+" "+CUR_Value
        //            //OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> CUR_ValueIndex (MLS_Status)  
        //            //			OPP-COMPANY-0						OPP-PRODUCT-0

        //            if (!doRS.IsLoaded("LNK_CreditedTo_US"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("LNK_For_CO"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_For_CO");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("LNK_For_PD"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_For_PD");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("DTT_Time"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("CUR_Value"))
        //            {

        //                goErr.SetError(35103, sProc, "", sFileName + ".CUR_Value");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("MLS_Status"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".MLS_Status");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }

        //            //LNK_CreditedTo_US%%TXT_Code
        //            sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp))
        //            {
        //                //No records linked
        //                sTemp = "?";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp = doLink.GetFieldVal("TXT_Code").ToString();
        //                }
        //                else
        //                {
        //                    sTemp = "?";
        //                }
        //            }

        //            //LNK_For_CO%%TXT_CompanyName
        //            sTemp2 = doRS.GetFieldVal("LNK_For_CO", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp2))
        //            {
        //                //No records linked
        //                sTemp2 = "";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp2 = doLink.GetFieldVal("TXT_CompanyName", 0, 22).ToString();
        //                }
        //                else
        //                {
        //                    sTemp2 = "";
        //                }
        //            }

        //            //LNK_For_Product%%TXT_ProductName
        //            sTemp3 = doRS.GetFieldVal("LNK_For_PD", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp3))
        //            {
        //                //No records linked
        //                sTemp3 = "";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("PD", 3, "GID_ID='" + sTemp3 + "'", "", "TXT_ProductName", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp3 = doLink.GetFieldVal("TXT_ProductName", 0, 14).ToString();
        //                }
        //                else
        //                {
        //                    sTemp3 = "";
        //                }
        //            }

        //            //Company (23)   '25
        //            //Date (15)      '11
        //            //Credited To User (5)
        //            //Product (15)   '17
        //            //Value (13)
        //            //Status (9)
        //            //*** MI 10/4/07 Added LocalToUTC conversion
        //            //sResult = sTemp2 & " " & _
        //            //    goTR.DateToString(doRS.GetFieldVal("DTE_Time", clC.SELL_SYSTEM), "YYYY-MM-DD") & " " & _
        //            //    sTemp & " " & _
        //            //    sTemp3 & " " & _
        //            //    goTR.Pad(doRS.GetFieldVal("CUR_Value"), 11, " ", "L")
        //            DateTime dttttime = Convert.ToDateTime(doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
        //            DateTime gotrdatedtttiem = Convert.ToDateTime(goTR.UTC_LocalToUTC(ref dttttime));
        //            par_iValid = 4;
        //            par_sDelim = " ";

        //            sResult = sTemp2 + " " + Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(gotrdatedtttiem, ref par_iValid, ref par_sDelim), 10) + " GMT " + sTemp + " " + sTemp3 + " " + goTR.Pad(doRS.GetFieldVal("CUR_Value").ToString(), 11, " ", "L");

        //            sResult += " [" + doRS.GetFieldVal("MLS_STATUS", 0, 8).ToString() + "]";

        //            break;

        //        case "OL":

        //            sTemp = goTR.Pad(doRS.GetFieldVal("SR__LineNo", clC.SELL_FRIENDLY).ToString(), 6, " ", "L", true, "R");
        //            //sTemp = doRS.GetFieldVal("SR__LineNo", clC.SELL_FRIENDLY).ToString();


        //            sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_FOR_PD%%TXT_PRODUCTNAME"));
        //            sTemp3 = Convert.ToString(doRS.GetFieldVal("SR__Quantity", 2));
        //            //sTemp4 = Convert.ToString(doRS.GetFieldVal("TXT_UNIT"));

        //            sResult = "#" + sTemp + ",  " + sTemp2 + ",  " + sTemp3;
        //            par_bRunNext = false;
        //            break;



        //        case "QT":
        //            //==> QUOTE NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+
        //            //					LNK_To_Company%%TXT_CompanyName+" "+CUR_Total

        //            if (!doRS.IsLoaded("LNK_CreditedTo_US"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("LNK_To_CO"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_To_CO");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("DTT_Time"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("TXT_QuoteNo"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".TXT_QuoteNo");
        //                ///35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("CUR_Total"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".CUR_Total");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("MLS_STATUS"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".MLS_STATUS");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }

        //            //LNK_CreditedTo_US%%TXT_Code
        //            sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp))
        //            {
        //                //No records linked
        //                sTemp = "?";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp = doLink.GetFieldVal("TXT_Code").ToString();
        //                }
        //                else
        //                {
        //                    sTemp = "?";
        //                }
        //            }

        //            //LNK_To_CO%%TXT_CompanyName
        //            sTemp2 = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp2))
        //            {
        //                //No records linked
        //                sTemp2 = "?";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp2 = doLink.GetFieldVal("TXT_CompanyName").ToString();
        //                }
        //                else
        //                {
        //                    sTemp2 = "?";
        //                }
        //            }


        //            //Company 17 '21
        //            //Date 15    '11
        //            //Cred User 6
        //            //Quote No 16
        //            //Total 15
        //            //Status 11
        //            //Total: 80
        //            sResult = goTR.Pad(sTemp2, 16, "", "R", true) + " " + goTR.Pad(sTemp3, 14, "", "R", true) + " " + goTR.Pad(sTemp, 4, "", "R", true) + " [" + goTR.Pad(doRS.GetFieldVal("TXT_QuoteNo").ToString(), 14, "", "R", true) + "] " + goTR.Pad(doRS.GetFieldVal("CUR_Total").ToString(), 13, "", "L", true) + " [" + doRS.GetFieldVal("MLS_STATUS", 0, 10).ToString() + "]";

        //            break;




        //    }



        //    par_oReturn = sResult;

        //    return true;

        //}

        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECLOSED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    //doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool Opp_CreateActLog_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // 12/22/2004 RAH: turned over to MI
            // 2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            // PURPOSE:
            // TLD 6/11/2010 Sends alert once resulting AC log is created
            // from journal entry.  Couldn't get new AC type journal to be
            // recognized when using an OPP_CreateActLog_Post
            // Run from enforce only in non-CREATION (MODIF) mode.
            // If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            // RETURNS:
            // 1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            // TLD 6/11/2010 Prevent main from running
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sNotes = "";
            string sWork = "";
            long lWork = 0;
            string sMessage;

            // 'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If


            if (Strings.Len(doForm.doRS.GetFieldVal("MMR_JOURNAL").ToString()) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
                return true;
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Opportunity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                return true;
            }

            doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");

            // sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // hard returns in the journal field
            sWork = doForm.oVar.GetVar("JournalWithHardReturns").ToString();
            // CS 2/4/10
            if (sWork == "")
                return true; // We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, null, null, null, -1, null, null, null, null, null, doForm.doRS.bBypassValidation);

            // 'sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // 'CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // 'hard returns in the journal field
            // sWork = doForm.oVar.GetVar("JournalWithHardReturns")

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork));
            sNotes = sNotes + "== Created from Opportunity '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            // TLD 4/25/2011 Notes tab & field removed from form, but left this just in case
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // CS doNew.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_Time"))
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));

            // In the code below Paul added IsObjectAssigned tests because SetLinkVals weren't working in some cases.
            // ==> Remove the IsObjectAssigned tests.

            doLink = doForm.doRS.GetLinkVal("LNK_ORIGINATEDBY_CN", ref doLink, true, 0, -1, "", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (10).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_Related_CN", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (11).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If

            doLink = doForm.doRS.GetLinkVal("LNK_FOR_PD", ref doLink, true, 0, -1, "", ref oTable);    // PJ Changed link name from 'LNK_RELATED_PRODUCT' to 'LNK_FOR_PRODUCT'
                                                                                                       // If Not goP.IsObjectAssigned(doLink) Then
                                                                                                       // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (12).", "", sProc)
                                                                                                       // '	goErr.DisplayLastError()
                                                                                                       // End If
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (13).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If

            doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GR) is not assigned.", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GROUP) is not assigned (2).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If

            doLink = doForm.doRS.GetLinkVal("LNK_FOR_CO", ref doLink, true, 0, -1, "", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_FOR_CO) is not assigned (3).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_RELATED_CO", doLink);
            // if goErr.GetLastError()<>"E00000" then
            // goErr.DisplayLastError()
            // End If

            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(doNew.GetFieldVal("MMO_HISTORY").ToString(), "Created."));
            doNew.SetFieldVal("LNK_RELATED_OP", doForm.GetRecordID());

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);

                goLog.Log("MessageBoxEvent", doForm.oVar.GetVar("ScriptMessages").ToString(), 1, false/* Conversion error: Set to default value for this argument */, true);
                return false;
            }
            else
            {
                // TLD 6/21/2011 New Tower Wet & New Dry Cooling no longer exit
                // New Tower Wet is Now Field Erected, so changed to that
                // TLD 6/11/2010 if successful, send alert
                // Only send alert if Credited to User is NOT current user
                // and User is a member of New Tower Wet or New Dry Cooling Divisions
                string sCreditedUSID = doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%GID_ID").ToString();
                if (sCreditedUSID != goP.GetMe("ID"))
                {
                    // Get RS for US
                    clRowSet doUSRS = new clRowSet("US", 3, "GID_ID='" + sCreditedUSID + "'", null/* Conversion error: Set to default value for this argument */, "LNK_RELATED_DV%%TXT_DIVISIONNAME", 1);
                    if (doUSRS.GetFirst() == 1)
                    {
                        // If doUSRS.GetFieldVal("LNK_RELATED_DV%%TXT_DIVISIONNAME") = "New Tower Wet" Or doUSRS.GetFieldVal("LNK_RELATED_DV%%TXT_DIVISIONNAME") = "New Dry Cooling" Then
                        string sDivisions = doUSRS.GetFieldVal("LNK_RELATED_DV%%TXT_DIVISIONNAME").ToString();
                        if (Strings.InStr(sDivisions, "Field Erected") > 0)
                        {
                            // Send alert
                            string sID = doNew.GetFieldVal("GID_ID").ToString();
                            goUI.AddAlert("OPP Journal Alert", clC.SELL_ALT_OPENRECORD, sID, doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US").ToString(), "Activity16.gif");
                        }
                    }
                    doUSRS = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            doNew = null;
            doLink = null;
            //par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;
        }


    }
}
