﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
using Newtonsoft.Json;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Data.SqlClient;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";
        SqlConnection par_oConnection = null;
        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();
        }
        public ScriptsCustom()
        {
            Initialize();
        }
        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //*** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //try
            //{


            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }

            //}

            return true;

        }

        //AC
        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        //AP

        public bool AP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // clForm doForm = par_doCallingObject;
            Form doForm = (Form)par_doCallingObject;

            // TLD 3/7/2014 If CRL from IE, set default fields
            if (doForm.GetMode() == "CREATION")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                // If the ID is not blank, find out if coming from an AP
                if (sID != "" & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) == "IE")
                    {
                        doForm.doRS.SetFieldVal("MLS_Type", 15, 2); // PM Service
                                                                    // Get IE RS
                        clRowSet doIERS = new clRowSet("IE", 3, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "DTE_NextService, LNK_ServiceRep_US, TXT_InstalledEquipmentName, MLS_Type, LNK_Customer_CN", 1);
                        if (doIERS.GetFirst() == 1)
                        {                          
                            doForm.doRS.SetFieldVal("DTE_StartTime", goData.GetFieldValueFromRec(sID, "DTE_NextService"));
                            doForm.doRS.SetFieldVal("TXT_Description", (doIERS.GetFieldVal("TXT_InstalledEquipmentName").ToString()+ " " +Convert.ToInt32( doIERS.GetFieldVal("MLS_Type", 1))));
                            doForm.doRS.SetFieldVal("CHK_Alarm", 1, 2);
                            doForm.doRS.SetFieldVal("LNK_CoordinatedBy_US", doIERS.GetFieldVal("LNK_ServiceRep_US", 2), 2);
                            doForm.doRS.SetFieldVal("LNK_With_CN", doIERS.GetFieldVal("LNK_Customer_CN", 2), 2);
                            doForm.doRS.SetFieldVal("LNK_Related_CO", doIERS.GetFieldVal("LNK_Customer_CO", 2), 2);
                            doIERS = null/* TODO Change to default(_) if this is not a reference type */;
                        }
                    }
                }
            }

            return true;
        }
        public bool AutoCreateServiceAlert_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 10/29/07 Replaced code with goTr.UTC_GetUserTimeZone().
            // MI 10/23/07 Cleaned up the script, implemented UTC.
            // MI 10/1/07 Change this script from overriding the main script to only running our customer
            // functionality and then running the main script. Commented out all the code that was identical
            // as in the main script.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: In a _Pre script set to False to prevent the main clScripts script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 3/7/2014 Create Service Alerts for Installed Equipment
            // when Active is Checked and Next Service Date is before 
            // 2 weeks from today so user can create APs
            // To:  LNK_ServiceRep_US
            par_bRunNext = false;

            // A_01_EXECUTE=AutoCreateServiceAlert
            // A_01_OBJSHARED = 1
            // A_01_TYPE = Script
            // A_ORDER=1,
            // ACTIONS=1,
            // ACTIVE = 1
            // E_TM_HOUR = 3
            // E_TM_INTERVAL = 3
            // E_TM_MINUTE = 30
            // E_TM_MISSED = 1
            // EVENT=TIMER
            // US_NAME=AutoCreateServiceAlert
            // US_PURPOSE=Create Service Alerts Daily when today is 2 weeks before Next Service Date for Installed Equipment
            // SORTVALUE1=TIMER_ACTIVE

            try
            {
                clRowSet doRS;
                DateTime dtDateTime;
                string sPointers;
                string sPointerDateTime;
                DateTime dtPointerDate;
                string sDateTime;
                long lBiID = 0;
                int iFailedOther = 0;
                int iFailedPerm = 0;
                int iFailedTotal = 0;
                int iSuccess = 0;
                string sBlankDate = clC.SELL_BLANK_SYSDATETIME;
                string sIEID = ""; // Installed Equipment record ID
                DateTime dtService; // Two weeks before today

                // --------- Set 2 weeks before today -------------
                // sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
                // in the currently processed user's time zone
                dtDateTime = goTR.NowUTC().Date;
                int par_iValid = 4;
                string par_sDelim = "|";
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);
                dtService = goTR.AddDay(goTR.NowUTC().Date, 14);

                sPointers = goMeta.PageRead("GLOBAL", "OTH_CUSTOM_DAILY_SCRIPT_PROCESSED");
                sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, "CreateServiceAlert", "", false), 10);
                if (sPointerDateTime == "")
                    // Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                    dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
                else
                    dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
                dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);
                // dtPointerDate = goTR.AddDay(dtPointerDate, 1) 'Testing

                if (dtPointerDate == dtDateTime)
                    return true;

                // 3) Installed Equipment
                // VS 02262015 TKT#369 : Send Alerts & Emails to CustomWOP Recipients
                string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", null, true, "XX");
                string sWOPRecepeints = goTR.StrRead(sWOP, "IE_SERVICEALERTRECIPIENTS");
                string[] clWOPRecepeints = System.Text.RegularExpressions.Regex.Split(sWOPRecepeints, Environment.NewLine);
                string sSubject = "Equipment Service is Due";
                string sBody = "";
                string sTo = "";
                string sFromName = "L&B Service Alerts";
                string sFromEmail = "<EMAIL>"; //"<EMAIL>";
                clEmail oEmail = new clEmail();
                clRowSet doUSRS;
                bool bEmail = false;

                if (sFromEmail == "")
                    sFromEmail = "<EMAIL>";
                if (sFromName == "")
                    sFromName = "DO NOT REPLY";

                do
                {
                    doRS = new clRowSet("IE", 1, "CHK_ActiveField=1 AND DTT_NextService<>'" + sBlankDate + "' AND DTT_NextService<" + dtService + " AND bi__id > " + lBiID + "", "bi__id asc", "LNK_ServiceRep_US,LNK_Customer_CO,TXT_INSTALLEDEQUIPMENTNAME,DTT_NEXTSERVICE", 50, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true, true, true);
                    if (doRS.GetFirst() == 1)
                    {
                        lBiID = Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                        do
                        {
                            // Check for existing Appointment
                            sIEID = doRS.GetFieldVal("GID_ID").ToString();
                            clRowSet doAPRS = new clRowSet("AP", 3, "LNK_Related_IE='" + sIEID + "' AND MLS_Type=15 AND CHK_Completed<>1", null/* Conversion error: Set to default value for this argument */, "GID_ID", 1);
                            if (doAPRS.GetFirst() == 1)
                            {
                            }
                            else
                            {
                                // Alert user to schedule appointment
                                goUI.AddAlert("Schedule Service", clC.SELL_ALT_OPENRECORD, sIEID, doRS.GetFieldVal("LNK_ServiceRep_US").ToString(), "Servic16.gif");

                                // Add Alerts & Emails to CustomWOP Recipients
                                for (int i = 0; i <= clWOPRecepeints.Length - 1; i++)
                                {
                                    if (clWOPRecepeints[i].Trim() != "")
                                    {

                                        if (clWOPRecepeints[i].Contains("@"))
                                        {
                                            string[] Recepeints = clWOPRecepeints[i].Split('@');
                                            string Rname = Recepeints[1].ToString();
                                            if (Rname.Contains("."))
                                                bEmail = true;
                                            else
                                                bEmail = false;
                                        }
                                        else
                                            bEmail = false;
                                        if (bEmail == true)
                                        {
                                            sTo = clWOPRecepeints[i].Trim();
                                            sBody = "The following equipment is due for service and a service appointment has not been scheduled.<br/>" + "<br/> Customer: " + doRS.GetFieldVal("LNK_Customer_CO%%SYS_Name") + "<br/> Equipment: " + doRS.GetFieldVal("TXT_INSTALLEDEQUIPMENTNAME") + "<br/> Service Date: " + doRS.GetFieldVal("DTE_NEXTSERVICE") + "<br/><br/>" + "<Span style=\"font-size:12px\">This is an automated alert sent to you because your e-mail address is on a list of recipients for service alerts. If you no longer wish to receive this e-mail, please contact your Selltis administrator.</Span>";
                                            if (oEmail.SendSMTPEmail(sSubject, sBody, sTo, "", "", "", sFromName, sFromEmail, null, true) == true)
                                                goLog.Log(sProc, "Email Successful to " + sTo + " for Installed Equipment" + doRS.GetFieldVal("GID_ID"), 1, false, true);
                                            else
                                                goLog.Log(sProc, "Email Failed to " + sTo + " for Installed Equipment " + doRS.GetFieldVal("GID_ID") + " with error " + goErr.GetLastError(), 1/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
                                        }
                                        else
                                        {
                                            string[] fullname = clWOPRecepeints[i].Split(',');
                                            string Lname = fullname[0].ToString();
                                            string Fname = fullname[1].ToString();


                                            if (clWOPRecepeints[i].Contains(","))
                                                doUSRS = new clRowSet("US", 3, "CHK_ACTIVEFIELD=1 AND TXT_NAMEFIRST='" + Lname + "' AND TXT_NAMELAST='" + Fname.Trim() + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID");
                                            else
                                                doUSRS = new clRowSet("US", 3, "CHK_ACTIVEFIELD=1 AND TXT_NAMELAST='" + clWOPRecepeints[i].Trim() + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID");
                                            if (doUSRS.GetFirst() == 1)
                                            {
                                                do
                                                {
                                                    goUI.AddAlert("Schedule Service", clC.SELL_ALT_OPENRECORD, sIEID, doUSRS.GetFieldVal("GID_ID").ToString(), "Servic16.gif");
                                                    if (doUSRS.GetNext() == 0)
                                                        break;
                                                }
                                                while (true);
                                            }

                                            doUSRS = null/* TODO Change to default(_) if this is not a reference type */;
                                        }
                                    }
                                }
                            }

                            if (doRS.GetNext() == 0)
                                break;
                        }
                        while (true);
                        lBiID = Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    }
                    else
                        break;
                }
                while (true)// get last BI__ID processed
    ;

                // Check once more for any newly added records
                doRS = new clRowSet("IE", 1, "CHK_ActiveField=1 AND DTT_NextService<>'" + sBlankDate + "' AND DTT_NextService<" + dtDateTime + " AND bi__id > " + lBiID + "", "bi__id", "LNK_ServiceRep_US", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true, true, true);
                if (doRS.GetFirst() == 1)
                {
                    do
                    {
                        // Check for existing Appointment
                        sIEID = doRS.GetFieldVal("GID_ID").ToString();
                        clRowSet doAPRS = new clRowSet("AP", 3, "LNK_Related_IE='" + sIEID + "' AND MLS_Type=15 AND CHK_Completed<>1", null/* Conversion error: Set to default value for this argument */, "GID_ID", 1);
                        if (doAPRS.GetFirst() == 1)
                        {
                        }
                        else
                        {
                            // Alert user to schedule appointment
                            goUI.AddAlert("Schedule Service", clC.SELL_ALT_OPENRECORD, sIEID, doRS.GetFieldVal("LNK_ServiceRep_US").ToString(), "Servic16.gif");

                            // Add Alerts & Emails to CustomWOP Recipients
                            for (int i = 0; i <= clWOPRecepeints.Length - 1; i++)
                            {
                                if (clWOPRecepeints[i].Trim() != "")
                                {
                                    // Email
                                    if (clWOPRecepeints[i].Contains("@"))
                                    {
                                        string[] Recepeints = clWOPRecepeints[i].Split('@');
                                        string Rname = Recepeints[1].ToString();

                                        if (Rname.Contains("."))
                                            bEmail = true;
                                        else
                                            bEmail = false;
                                    }
                                    else
                                        bEmail = false;
                                    if (bEmail == true)
                                    {
                                        sTo = clWOPRecepeints[i].Trim();
                                        sBody = "The following equipment is due for service and a service appointment has not been scheduled.<br/>" + "<br/> Customer: " + doRS.GetFieldVal("LNK_Customer_CO%%SYS_Name") + "<br/> Equipment: " + doRS.GetFieldVal("TXT_INSTALLEDEQUIPMENTNAME") + "<br/> Service Date: " + doRS.GetFieldVal("DTE_NEXTSERVICE") + "<br/><br/>" + "<Span style=\"font-size:12px\">This is an automated alert sent to you because your e-mail address is on a list of recipients for service alerts. If you no longer wish to receive this e-mail, please contact your Selltis administrator.</Span>";
                                        if (oEmail.SendSMTPEmail(sSubject, sBody, sTo, "", "", "", sFromName, sFromEmail, null/* Conversion error: Set to default value for this argument */, true) == true)
                                            goLog.Log(sProc, "Email Successful to " + sTo + " for Installed Equipment" + doRS.GetFieldVal("GID_ID"), 1/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
                                        else
                                            goLog.Log(sProc, "Email Failed to " + sTo + " for Installed Equipment " + doRS.GetFieldVal("GID_ID") + " with error " + goErr.GetLastError(), 1/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
                                    }
                                    else
                                    {
                                        string[] fullname = clWOPRecepeints[i].Split(',');
                                        string Lname = fullname[0].ToString();
                                        string Fname = fullname[1].ToString();

                                        if (clWOPRecepeints[i].Contains(","))
                                            doUSRS = new clRowSet("US", 3, "CHK_ACTIVEFIELD=1 AND TXT_NAMEFIRST='" + Fname.Trim() + "' AND TXT_NAMELAST='" + Lname.Trim() + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID");
                                        else
                                            doUSRS = new clRowSet("US", 3, "CHK_ACTIVEFIELD=1 AND TXT_NAMELAST='" + clWOPRecepeints[i].Trim() + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID");
                                        if (doUSRS.GetFirst() == 1)
                                        {
                                            do
                                            {
                                                goUI.AddAlert("Schedule Service", clC.SELL_ALT_OPENRECORD, sIEID, doUSRS.GetFieldVal("GID_ID").ToString(), "Servic16.gif");
                                                if (doUSRS.GetNext() == 0)
                                                    break;
                                            }
                                            while (true);
                                        }

                                        doUSRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    }
                                }
                            }
                        }

                        if (doRS.GetNext() == 0)
                            break;
                    }
                    while (true);
                    lBiID = Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                }

                // Update the daily processing pointer with current datetime in the processed user's time zone
                goMeta.LineWrite("GLOBAL", "OTH_CUSTOM_DAILY_SCRIPT_PROCESSED", "CreateServiceAlert", goTR.NowServer(), ref par_oConnection);
            }
            catch (Exception ex)
            {
                // log
                goLog.Log(sProc, "failed with error " + goErr.GetLastError());
            }

            return true;
        }
        public bool AutoQuoteDuplicate_Post_Inactive(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Get record opened in the WA and set custom fields from orig QT
            // CS NOTE: I disabled this because QT_FormOnLoadRecord was then
            // overwriting many of these fields. If we need to do this we'll have
            // to customize QT_FormonLoadRecord not to do that in this case.

            string sID = goUI.GetLastSelected("SELECTEDRECORDID"); // duplicated QT
                                                                   // Set fields from ClonedFromQT link

            // Get duplicate QT that is waiting in the que
            //clArray doQue = new clArray();
            //doQue = goUI.GetQueuedGUIDs;

            //if (doQue.GetDimension() > 0)
            //{
            //    clForm oForm = goUI.GetUIObject(doQue.GetItem(1)); // Top item in que
            //    clRowSet doOrigQT = new clRowSet("QT", 3, "GID_ID=" + oForm.doRS.GetFieldVal("lnk_clonedfrom_qt") + "", null/* Conversion error: Set to default value for this argument */, "TXT_FOB,MLS_FOBSELECT,MLS_PROPSHIPSELECT,TXT_PROPOSEDSHIPPING,MLS_SHIPTERMSSELECT,TXT_SHIPPINGTERMS,MLS_VALIDSELECT");
            //    oForm.doRS.SetFieldVal("TXT_FOB", doOrigQT.GetFieldVal("TXT_FOB"));
            //    oForm.doRS.SetFieldVal("MLS_FOBSELECT", doOrigQT.GetFieldVal("MLS_FOBSELECT", 2), 2);
            //    oForm.doRS.SetFieldVal("MLS_PROPSHIPSELECT", doOrigQT.GetFieldVal("MLS_PROPSHIPSELECT", 2), 2);
            //    oForm.doRS.SetFieldVal("TXT_PROPOSEDSHIPPING", doOrigQT.GetFieldVal("TXT_PROPOSEDSHIPPING"));
            //    oForm.doRS.SetFieldVal("MLS_SHIPTERMSSELECT", doOrigQT.GetFieldVal("MLS_SHIPTERMSSELECT", 2), 2);
            //    oForm.doRS.SetFieldVal("TXT_SHIPPINGTERMS", doOrigQT.GetFieldVal("TXT_SHIPPINGTERMS"));
            //    oForm.doRS.SetFieldVal("MLS_VALIDSELECT", doOrigQT.GetFieldVal("MLS_VALIDSELECT", 2), 2);
            //}

            return true;
        }
        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        //CN

        public bool CN_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        //CO
        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // Check if user has permission for credit status
            clRowSet doRSUser = new clRowSet("US", 3, "GID_ID='" + goP.GetMe("ID") + "'", null/* Conversion error: Set to default value for this argument */, "CHK_PermCreditStatus");
            if (doRSUser.GetFirst() == 1)
            {
                if (Convert.ToInt32(doRSUser.GetFieldVal("CHK_PermCreditStatus", 2)) == 0)
                    doForm.SetControlState("MLS_CreditSTatus", 4);
                else
                    doForm.SetControlState("MLS_CreditSTatus", 0);
            }
            else
                // disable
                doForm.SetControlState("MLS_CreditStatus", 4);
            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }
        public bool IE_FormControlOnChange_BTN_PrintGeneratorReport_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //clForm doForm = par_doCallingObject;
            Form doForm = (Form)par_doCallingObject;

            // TLD 3/5/2014 Save if dirty, then print to MS Word template
            if (doForm.IsDirty)
            {
                // Save quote
                if (doForm.Save(3, true/* Conversion error: Set to default value for this argument */, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                {
                }
            }

            // Send to PC Link
            clSend oSend = new clSend();
            oSend.AddSendJob("Annual Generator Service " + doForm.doRS.GetFieldVal("SYS_Name") + " to Word", doForm.doRS.GetFieldVal("GID_ID").ToString(), "CUS_MS_WORD_GENERATOR.DOC", "CORR", "FILE", "IE", true, true);
            goUI.ExecuteSendPopup = true;
            par_doCallingObject = doForm;
            return true;
        }
        public bool IE_FormControlOnChange_BTN_PrintResidentialReport_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //clForm doForm = par_doCallingObject;
            Form doForm = (Form)par_doCallingObject;

            // TLD 3/5/2014 Save if dirty, then print to MS Word template
            if (doForm.IsDirty)
            {
                // Save quote
                if (doForm.Save(3, true/* Conversion error: Set to default value for this argument */, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                {
                }
            }

            // Send to PC Link
            clSend oSend = new clSend();
            oSend.AddSendJob("Residential Generator Service " + doForm.doRS.GetFieldVal("SYS_Name") + " to Word", doForm.doRS.GetFieldVal("GID_ID").ToString(), "CUS_MS_WORD_RESIDENTIAL.doc", "CORR", "FILE", "IE", true, true);
            goUI.ExecuteSendPopup = true;
            par_doCallingObject = doForm;
            return true;
        }
        //OP
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //clForm doForm = par_doCallingObject;
            Form doForm = (Form)par_doCallingObject;
            string sProduct;
            bool bHP = false;
            bool bRPM = false;
            bool bVolts = false;
            bool bFrame = false;
            bool bEncl = false;

            // Get Model's Product's Group
            sProduct = doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD").ToString();
            // Get Product's Group
            clRowSet doRSGroup = new clRowSet("PD", 3, "GID_ID='" + sProduct + "'", null/* Conversion error: Set to default value for this argument */, "LNK_RELATED_GR%%SYS_NAME");
            if (doRSGroup.GetFirst() == 1)
            {
                // Enforce fields if Group is a MOTOR or DRIVE
                string sGroup = doRSGroup.GetFieldVal("LNK_RELATEd_GR%%SYS_NAME").ToString();
                if (Strings.UCase(sGroup) == "DRIVE")
                {
                    bHP = true;
                    bRPM = true;
                    bVolts = true;
                    bFrame = true;
                    bEncl = true;
                }
                else if (Strings.UCase(sGroup) == "MOTOR")
                {
                    bHP = true;
                    bRPM = false;
                    bVolts = true;
                    bFrame = false;
                    bEncl = false;
                }
            }

            if (bRPM == true)
            {
                // Enforce RPM
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_RPM", 2)) == 0)
                {
                    doForm.MoveToTab(1, "MLS_RPM");
                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QL", "MLS_RPM"), "", "", "", "", "", "", "", "", "MLS_RPM");
                    return false;
                }
            }

            if (bHP == true)
            {
                // Enforce HP
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_HP", 2)) == 0)
                {
                    doForm.MoveToTab(1, "MLS_HP");
                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QL", "MLS_HP"), "", "", "", "", "", "", "", "", "MLS_HP");
                    return false;
                }
            }

            if (bVolts == true)
            {
                // Enforce Volts
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_VOLTS", 2)) == 0)
                {
                    doForm.MoveToTab(1, "MLS_VOLTS");
                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QL", "MLS_VOLTS"), "", "", "", "", "", "", "", "", "MLS_VOLTS");
                    return false;
                }
            }

            if (bFrame == true)
            {
                // Enforce frame
                if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_FRAME").ToString()))
                {
                    doForm.MoveToTab(1, "TXT_FRAME");
                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QL", "TXT_FRAME"), "", "", "", "", "", "", "", "", "TXT_FRAME");
                    return false;
                }
            }

            if (bEncl == true)
            {
                // Enforce Encl
                if (doForm.doRS.GetFieldVal("TXT_ENCL").ToString() == "")
                {
                    doForm.MoveToTab(1, "TXT_ENCL");
                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QL", "TXT_ENCL"), "", "", "", "", "", "", "", "", "TXT_ENCL");
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormAfterSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // MI 4/26/07

            Form doForm = (Form)par_doCallingObject;
            clRowSet doLines;
            string sRecID = doForm.GetRecordID();
            string sMessage;

            // TLD 1/13/2009 Updates QL Date Closed fields with QT Date Closed
            doLines = new clRowSet("QL", 1, "LNK_IN_QT='" + sRecID + "'", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true);
            if (doLines.GetFirst() == 1)
            {
                do
                {
                    // update QL Date Closed
                    doLines.SetFieldVal("DTE_DATECLOSED", doForm.doRS.GetFieldVal("DTE_DATECLOSED", 2));
                    if (doLines.Commit() != 1)
                    {
                        sMessage = "The Date Closed of one or more of the Quote lines can't be updated. Please contact your Selltis administrator. Quote Name: '" + doForm.doRS.GetFieldVal("SYS_NAME") + "'. Quote ID: '" + sRecID + "'.";
                        sMessage = sMessage + goErr.GetLastError("MESSAGE");
                        doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);
                    }

                    if (doLines.GetNext() == 0)
                        break;
                }
                while (true);
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_LNK_RELATED_TR_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sTerms;


            try
            {
                // Set Ship terms text to value in Related Terms (if not 'Other')
                sTerms = Strings.UCase(doForm.doRS.GetFieldVal("LNK_RELATED_TR%%SYS_NAME").ToString());
                if (sTerms != "OTHER")
                    doForm.doRS.SetFieldVal("TXT_TERMSTEXT", sTerms);
            }


            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool QT_FormControlOnChange_MLS_FOBSelect(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sFob;


            try
            {
                // Set FOB text
                sFob = Strings.UCase(doForm.doRS.GetFieldVal("MLS_FOBSELECT").ToString());
                if (sFob != "<MAKE SELECTION>" & sFob != "OTHER")
                    doForm.doRS.SetFieldVal("TXT_FOB", sFob);
            }


            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_MLS_PropShipSelect(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sPropShip;


            try
            {
                // Set Prop Ship text 
                sPropShip = Strings.UCase(doForm.doRS.GetFieldVal("MLS_PropShipselect").ToString());
                if (sPropShip != "<MAKE SELECTION>" & sPropShip != "OTHER")
                    doForm.doRS.SetFieldVal("TXT_PROPOSEDSHIPPING", sPropShip);
            }


            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_MLS_ShipTermsSelect(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sTerms;


            //try
            //{
            // Set Ship terms text
            sTerms = Strings.UCase(doForm.doRS.GetFieldVal("MLS_SHIPTERMSSELECT").ToString());
            if (sTerms != "<MAKE SELECTION>" & sTerms != "OTHER")
                doForm.doRS.SetFieldVal("TXT_SHIPPINGTERMS", sTerms);
            // }


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_MLS_ValidSelect(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            int iValid = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_VALIDSELECT", 2));
            DateTime dDate = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_TIME"));


            // Set Valid Until date based on 'valid Select'
            switch (iValid)
            {
                case 1 // 15
               :
                    {
                        doForm.doRS.SetFieldVal("DTE_ValidUntilDate", goTR.AddDay(dDate, 15));
                        break;
                    }

                case 2 // 30
         :
                    {
                        doForm.doRS.SetFieldVal("DTE_ValidUntilDate", goTR.AddDay(dDate, 30));
                        break;
                    }

                case 3 // 45
         :
                    {
                        doForm.doRS.SetFieldVal("DTE_ValidUntilDate", goTR.AddDay(dDate, 45));
                        break;
                    }

                case 4 // 60
         :
                    {
                        doForm.doRS.SetFieldVal("DTE_ValidUntilDate", goTR.AddDay(dDate, 60));
                        break;
                    }
            }

            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //clRowSet doRS = par_doCallingObject;

            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool XW_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", null/* Conversion error: Set to default value for this argument */, true/* Conversion error: Set to default value for this argument */, "XX");

            // TLD 5/30/2012 Added for AC New User Prompts
            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            // Read values from MD and set in the form fields
            // VS 02262015 TKT#369 : Load Installed Equipment Recipients
            doForm.SetControlVal("NDB_MMO_SERVICEALERTRECIPIENTS", goTR.StrRead(sWOP, "IE_SERVICEALERTRECIPIENTS"));
            par_doCallingObject = doForm;
            return true;
        }
        public bool XW_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "XX");

            // TLD 5/30/2012 Added for AC New User Prompts
            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            // Write values to MD
            // VS 02262015 TKT#369 : Save Installed Equipment Recipients
            goTR.StrWrite(ref sWOP, "IE_SERVICEALERTRECIPIENTS", doForm.GetControlVal("NDB_MMO_SERVICEALERTRECIPIENTS"));

            // TLD Write to Product XX
            goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "XX");

            doForm.CloseOnReturn = true;
            doForm.CancelSave();
            par_doCallingObject = doForm;
            return true;
        }


    }
}
